{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nexport default function channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    raf(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}", "map": {"version": 3, "names": ["raf", "channelUpdate", "callback", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "undefined"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-overflow/es/hooks/channelUpdate.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nexport default function channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    raf(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC9C,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;IACzCH,GAAG,CAACE,QAAQ,CAAC;EACf,CAAC,MAAM;IACL,IAAIE,OAAO,GAAG,IAAID,cAAc,CAAC,CAAC;IAClCC,OAAO,CAACC,KAAK,CAACC,SAAS,GAAG,YAAY;MACpC,OAAOJ,QAAQ,CAAC,CAAC;IACnB,CAAC;IACDE,OAAO,CAACG,KAAK,CAACC,WAAW,CAACC,SAAS,CAAC;EACtC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}