import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Input,
  Button,
  Table,
  Space,
  Typography,
  Tag,
  Collapse,
  message,
  Row,
  Col,
  Statistic,
  Spin
} from 'antd';
import {
  SearchOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons';
import { releaseApi } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

interface ReleaseData {
  檔案名稱: string;
  比較資訊: {
    專案ID: string;
    新分支: {
      branch_name: string;
      date: string;
      message: string;
      author: string;
    };
    舊分支: {
      branch_name: string;
      date: string;
      message: string;
      author: string;
    };
    比較時間: string;
    新增commit數量: number;
  };
  新增commit記錄: Array<{
    commit_hash: string;
    commit_訊息: string;
    提交日期: string;
    作者: string;
    檔案變更: Array<{
      檔案路徑: string;
      變更類型: string;
    }>;
    變更檔案數量: number;
  }>;
}

const ReleaseQuery: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<string[]>([]);
  const [branches, setBranches] = useState<string[]>([]);
  const [releaseData, setReleaseData] = useState<ReleaseData[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>('');

  // 載入專案列表
  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      const response = await releaseApi.getProjects();
      setProjects(response.data.projects);
    } catch (error) {
      message.error('載入專案列表失敗');
    }
  };

  // 載入分支列表
  const loadBranches = async (project: string) => {
    try {
      const response = await releaseApi.getBranches(project);
      setBranches(response.data.branches);
    } catch (error) {
      message.error('載入分支列表失敗');
    }
  };

  // 專案變更處理
  const handleProjectChange = (project: string) => {
    setSelectedProject(project);
    setBranches([]);
    form.setFieldsValue({ branch: undefined });
    loadBranches(project);
  };

  // 搜尋處理
  const handleSearch = async (values: any) => {
    if (!selectedProject) {
      message.warning('請先選擇專案');
      return;
    }

    setLoading(true);
    try {
      const params = {
        project: selectedProject,
        branch: values.branch,
        search: values.search,
        file_search: values.file_search,
      };

      const response = await releaseApi.getData(params);
      setReleaseData(response.data.data);
      message.success(`找到 ${response.data.total} 筆記錄`);
    } catch (error) {
      message.error('搜尋失敗');
    } finally {
      setLoading(false);
    }
  };

  // 重置搜尋
  const handleReset = () => {
    form.resetFields();
    setReleaseData([]);
    setBranches([]);
    setSelectedProject('');
  };

  // 表格欄位定義
  const columns = [
    {
      title: '檔案名稱',
      dataIndex: '檔案名稱',
      key: 'filename',
      width: 300,
      render: (text: string) => (
        <Text code style={{ fontSize: 12 }}>
          {text}
        </Text>
      ),
    },
    {
      title: '專案',
      dataIndex: ['比較資訊', '專案ID'],
      key: 'project',
      width: 100,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '分支比較',
      key: 'branches',
      width: 200,
      render: (_: any, record: ReleaseData) => (
        <Space direction="vertical" size="small">
          <Text strong>
            {record.比較資訊.新分支.branch_name} ← {record.比較資訊.舊分支.branch_name}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Commit 數量',
      dataIndex: ['比較資訊', '新增commit數量'],
      key: 'commit_count',
      width: 100,
      render: (count: number) => (
        <Statistic value={count} suffix="個" />
      ),
    },
    {
      title: '比較時間',
      dataIndex: ['比較資訊', '比較時間'],
      key: 'compare_time',
      width: 150,
      render: (time: string) => (
        <Space>
          <CalendarOutlined />
          <Text>{time}</Text>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <FileTextOutlined /> Release 記錄查詢工具
      </Title>

      <Card style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSearch}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="專案"
                name="project"
                rules={[{ required: true, message: '請選擇專案' }]}
              >
                <Select
                  placeholder="選擇專案"
                  onChange={handleProjectChange}
                  allowClear
                >
                  {projects.map(project => (
                    <Option key={project} value={project}>
                      {project}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item label="分支" name="branch">
                <Select
                  placeholder="選擇分支 (可選)"
                  disabled={!selectedProject}
                  allowClear
                >
                  {branches.map(branch => (
                    <Option key={branch} value={branch}>
                      {branch}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item label="Commit 訊息搜尋" name="search">
                <Input
                  placeholder="輸入關鍵字"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item label="檔案名稱搜尋" name="file_search">
                <Input
                  placeholder="輸入檔案名稱"
                  prefix={<FileTextOutlined />}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                搜尋
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={releaseData}
            rowKey="檔案名稱"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 筆記錄`,
            }}
            expandable={{
              expandedRowRender: (record: ReleaseData) => (
                <Collapse>
                  <Panel header="Commit 詳細記錄" key="commits">
                    {record.新增commit記錄.map((commit, index) => (
                      <Card key={index} size="small" style={{ marginBottom: 8 }}>
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Space>
                            <Tag color="green">{commit.commit_hash.substring(0, 8)}</Tag>
                            <UserOutlined />
                            <Text>{commit.作者}</Text>
                            <CalendarOutlined />
                            <Text>{commit.提交日期}</Text>
                          </Space>
                          <Text strong>{commit.commit_訊息}</Text>
                          <Text type="secondary">
                            變更檔案數量: {commit.變更檔案數量}
                          </Text>
                          {commit.檔案變更.length > 0 && (
                            <Collapse size="small">
                              <Panel header="檔案變更詳情" key="files">
                                {commit.檔案變更.map((file, fileIndex) => (
                                  <div key={fileIndex} style={{ marginBottom: 4 }}>
                                    <Tag color={file.變更類型 === 'A' ? 'green' : file.變更類型 === 'M' ? 'orange' : 'red'}>
                                      {file.變更類型}
                                    </Tag>
                                    <Text code>{file.檔案路徑}</Text>
                                  </div>
                                ))}
                              </Panel>
                            </Collapse>
                          )}
                        </Space>
                      </Card>
                    ))}
                  </Panel>
                </Collapse>
              ),
            }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default ReleaseQuery;
