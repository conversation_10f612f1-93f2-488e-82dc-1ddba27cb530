{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport BodyLine from \"./BodyLine\";\nimport { GridContext, StaticContext } from \"./context\";\nvar Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    onScroll = props.onScroll;\n  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction']),\n    flattenColumns = _useContext.flattenColumns,\n    onColumnResize = _useContext.onColumnResize,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    prefixCls = _useContext.prefixCls,\n    childrenColumnName = _useContext.childrenColumnName,\n    scrollX = _useContext.scrollX,\n    direction = _useContext.direction;\n  var _useContext2 = useContext(StaticContext),\n    sticky = _useContext2.sticky,\n    scrollY = _useContext2.scrollY,\n    listItemHeight = _useContext2.listItemHeight,\n    getComponent = _useContext2.getComponent,\n    onTablePropScroll = _useContext2.onScroll;\n\n  // =========================== Ref ============================\n  var listRef = React.useRef();\n\n  // =========================== Data ===========================\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // ========================== Column ==========================\n  var columnsWidth = React.useMemo(function () {\n    var total = 0;\n    return flattenColumns.map(function (_ref) {\n      var width = _ref.width,\n        minWidth = _ref.minWidth,\n        key = _ref.key;\n      var finalWidth = Math.max(width || 0, minWidth || 0);\n      total += finalWidth;\n      return [key, finalWidth, total];\n    });\n  }, [flattenColumns]);\n  var columnsOffset = React.useMemo(function () {\n    return columnsWidth.map(function (colWidth) {\n      return colWidth[2];\n    });\n  }, [columnsWidth]);\n  React.useEffect(function () {\n    columnsWidth.forEach(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n        key = _ref3[0],\n        width = _ref3[1];\n      onColumnResize(key, width);\n    });\n  }, [columnsWidth]);\n\n  // =========================== Ref ============================\n  React.useImperativeHandle(ref, function () {\n    var _listRef$current2;\n    var obj = {\n      scrollTo: function scrollTo(config) {\n        var _listRef$current;\n        (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(config);\n      },\n      nativeElement: (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 ? void 0 : _listRef$current2.nativeElement\n    };\n    Object.defineProperty(obj, 'scrollLeft', {\n      get: function get() {\n        var _listRef$current3;\n        return ((_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 ? void 0 : _listRef$current3.getScrollInfo().x) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current4;\n        (_listRef$current4 = listRef.current) === null || _listRef$current4 === void 0 || _listRef$current4.scrollTo({\n          left: value\n        });\n      }\n    });\n\n    // https://github.com/ant-design/ant-design/issues/54734\n    Object.defineProperty(obj, 'scrollTop', {\n      get: function get() {\n        var _listRef$current5;\n        return ((_listRef$current5 = listRef.current) === null || _listRef$current5 === void 0 ? void 0 : _listRef$current5.getScrollInfo().y) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current6;\n        (_listRef$current6 = listRef.current) === null || _listRef$current6 === void 0 || _listRef$current6.scrollTo({\n          top: value\n        });\n      }\n    });\n    return obj;\n  });\n\n  // ======================= Col/Row Span =======================\n  var getRowSpan = function getRowSpan(column, index) {\n    var _flattenData$index;\n    var record = (_flattenData$index = flattenData[index]) === null || _flattenData$index === void 0 ? void 0 : _flattenData$index.record;\n    var onCell = column.onCell;\n    if (onCell) {\n      var _cellProps$rowSpan;\n      var cellProps = onCell(record, index);\n      return (_cellProps$rowSpan = cellProps === null || cellProps === void 0 ? void 0 : cellProps.rowSpan) !== null && _cellProps$rowSpan !== void 0 ? _cellProps$rowSpan : 1;\n    }\n    return 1;\n  };\n  var extraRender = function extraRender(info) {\n    var start = info.start,\n      end = info.end,\n      getSize = info.getSize,\n      offsetY = info.offsetY;\n\n    // Do nothing if no data\n    if (end < 0) {\n      return null;\n    }\n\n    // Find first rowSpan column\n    var firstRowSpanColumns = flattenColumns.filter(\n    // rowSpan is 0\n    function (column) {\n      return getRowSpan(column, start) === 0;\n    });\n    var startIndex = start;\n    var _loop = function _loop(i) {\n      firstRowSpanColumns = firstRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, i) === 0;\n      });\n      if (!firstRowSpanColumns.length) {\n        startIndex = i;\n        return 1; // break\n      }\n    };\n    for (var i = start; i >= 0; i -= 1) {\n      if (_loop(i)) break;\n    }\n\n    // Find last rowSpan column\n    var lastRowSpanColumns = flattenColumns.filter(\n    // rowSpan is not 1\n    function (column) {\n      return getRowSpan(column, end) !== 1;\n    });\n    var endIndex = end;\n    var _loop2 = function _loop2(_i) {\n      lastRowSpanColumns = lastRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, _i) !== 1;\n      });\n      if (!lastRowSpanColumns.length) {\n        endIndex = Math.max(_i - 1, end);\n        return 1; // break\n      }\n    };\n    for (var _i = end; _i < flattenData.length; _i += 1) {\n      if (_loop2(_i)) break;\n    }\n\n    // Collect the line who has rowSpan\n    var spanLines = [];\n    var _loop3 = function _loop3(_i2) {\n      var item = flattenData[_i2];\n\n      // This code will never reach, just incase\n      if (!item) {\n        return 1; // continue\n      }\n      if (flattenColumns.some(function (column) {\n        return getRowSpan(column, _i2) > 1;\n      })) {\n        spanLines.push(_i2);\n      }\n    };\n    for (var _i2 = startIndex; _i2 <= endIndex; _i2 += 1) {\n      if (_loop3(_i2)) continue;\n    }\n\n    // Patch extra line on the page\n    var nodes = spanLines.map(function (index) {\n      var item = flattenData[index];\n      var rowKey = getRowKey(item.record, index);\n      var getHeight = function getHeight(rowSpan) {\n        var endItemIndex = index + rowSpan - 1;\n        var endItemKey = getRowKey(flattenData[endItemIndex].record, endItemIndex);\n        var sizeInfo = getSize(rowKey, endItemKey);\n        return sizeInfo.bottom - sizeInfo.top;\n      };\n      var sizeInfo = getSize(rowKey);\n      return /*#__PURE__*/React.createElement(BodyLine, {\n        key: index,\n        data: item,\n        rowKey: rowKey,\n        index: index,\n        style: {\n          top: -offsetY + sizeInfo.top\n        },\n        extra: true,\n        getHeight: getHeight\n      });\n    });\n    return nodes;\n  };\n\n  // ========================= Context ==========================\n  var gridContext = React.useMemo(function () {\n    return {\n      columnsOffset: columnsOffset\n    };\n  }, [columnsOffset]);\n\n  // ========================== Render ==========================\n  var tblPrefixCls = \"\".concat(prefixCls, \"-tbody\");\n\n  // default 'div' in rc-virtual-list\n  var wrapperComponent = getComponent(['body', 'wrapper']);\n\n  // ========================== Sticky Scroll Bar ==========================\n  var horizontalScrollBarStyle = {};\n  if (sticky) {\n    horizontalScrollBarStyle.position = 'sticky';\n    horizontalScrollBarStyle.bottom = 0;\n    if (_typeof(sticky) === 'object' && sticky.offsetScroll) {\n      horizontalScrollBarStyle.bottom = sticky.offsetScroll;\n    }\n  }\n  return /*#__PURE__*/React.createElement(GridContext.Provider, {\n    value: gridContext\n  }, /*#__PURE__*/React.createElement(VirtualList, {\n    fullHeight: false,\n    ref: listRef,\n    prefixCls: \"\".concat(tblPrefixCls, \"-virtual\"),\n    styles: {\n      horizontalScrollBar: horizontalScrollBarStyle\n    },\n    className: tblPrefixCls,\n    height: scrollY,\n    itemHeight: listItemHeight || 24,\n    data: flattenData,\n    itemKey: function itemKey(item) {\n      return getRowKey(item.record);\n    },\n    component: wrapperComponent,\n    scrollWidth: scrollX,\n    direction: direction,\n    onVirtualScroll: function onVirtualScroll(_ref4) {\n      var _listRef$current7;\n      var x = _ref4.x;\n      onScroll({\n        currentTarget: (_listRef$current7 = listRef.current) === null || _listRef$current7 === void 0 ? void 0 : _listRef$current7.nativeElement,\n        scrollLeft: x\n      });\n    },\n    onScroll: onTablePropScroll,\n    extraRender: extraRender\n  }, function (item, index, itemProps) {\n    var rowKey = getRowKey(item.record, index);\n    return /*#__PURE__*/React.createElement(BodyLine, {\n      data: item,\n      rowKey: rowKey,\n      index: index,\n      style: itemProps.style\n    });\n  }));\n});\nvar ResponseGrid = responseImmutable(Grid);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseGrid.displayName = 'ResponseGrid';\n}\nexport default ResponseGrid;", "map": {"version": 3, "names": ["_typeof", "_slicedToArray", "useContext", "VirtualList", "React", "TableContext", "responseImmutable", "useFlattenRecords", "BodyLine", "GridContext", "StaticContext", "Grid", "forwardRef", "props", "ref", "data", "onScroll", "_useContext", "flattenColumns", "onColumnResize", "getRowKey", "expandedKeys", "prefixCls", "childrenColumnName", "scrollX", "direction", "_useContext2", "sticky", "scrollY", "listItemHeight", "getComponent", "onTablePropScroll", "listRef", "useRef", "flattenData", "columnsWidth", "useMemo", "total", "map", "_ref", "width", "min<PERSON><PERSON><PERSON>", "key", "finalWidth", "Math", "max", "columnsOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "for<PERSON>ach", "_ref2", "_ref3", "useImperativeHandle", "_listRef$current2", "obj", "scrollTo", "config", "_listRef$current", "current", "nativeElement", "Object", "defineProperty", "get", "_listRef$current3", "getScrollInfo", "x", "set", "value", "_listRef$current4", "left", "_listRef$current5", "y", "_listRef$current6", "top", "getRowSpan", "column", "index", "_flattenData$index", "record", "onCell", "_cellProps$rowSpan", "cellProps", "rowSpan", "extraRender", "info", "start", "end", "getSize", "offsetY", "firstRowSpanColumns", "filter", "startIndex", "_loop", "i", "length", "lastRowSpanColumns", "endIndex", "_loop2", "_i", "spanLines", "_loop3", "_i2", "item", "some", "push", "nodes", "<PERSON><PERSON><PERSON>", "getHeight", "endItemIndex", "endItemKey", "sizeInfo", "bottom", "createElement", "style", "extra", "gridContext", "tblPrefixCls", "concat", "wrapperComponent", "horizontalScrollBarStyle", "position", "offsetScroll", "Provider", "fullHeight", "styles", "horizontalScrollBar", "className", "height", "itemHeight", "itemKey", "component", "scrollWidth", "onVirtualScroll", "_ref4", "_listRef$current7", "currentTarget", "scrollLeft", "itemProps", "ResponseGrid", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-table/es/VirtualTable/BodyGrid.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport BodyLine from \"./BodyLine\";\nimport { GridContext, StaticContext } from \"./context\";\nvar Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    onScroll = props.onScroll;\n  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction']),\n    flattenColumns = _useContext.flattenColumns,\n    onColumnResize = _useContext.onColumnResize,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    prefixCls = _useContext.prefixCls,\n    childrenColumnName = _useContext.childrenColumnName,\n    scrollX = _useContext.scrollX,\n    direction = _useContext.direction;\n  var _useContext2 = useContext(StaticContext),\n    sticky = _useContext2.sticky,\n    scrollY = _useContext2.scrollY,\n    listItemHeight = _useContext2.listItemHeight,\n    getComponent = _useContext2.getComponent,\n    onTablePropScroll = _useContext2.onScroll;\n\n  // =========================== Ref ============================\n  var listRef = React.useRef();\n\n  // =========================== Data ===========================\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // ========================== Column ==========================\n  var columnsWidth = React.useMemo(function () {\n    var total = 0;\n    return flattenColumns.map(function (_ref) {\n      var width = _ref.width,\n        minWidth = _ref.minWidth,\n        key = _ref.key;\n      var finalWidth = Math.max(width || 0, minWidth || 0);\n      total += finalWidth;\n      return [key, finalWidth, total];\n    });\n  }, [flattenColumns]);\n  var columnsOffset = React.useMemo(function () {\n    return columnsWidth.map(function (colWidth) {\n      return colWidth[2];\n    });\n  }, [columnsWidth]);\n  React.useEffect(function () {\n    columnsWidth.forEach(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n        key = _ref3[0],\n        width = _ref3[1];\n      onColumnResize(key, width);\n    });\n  }, [columnsWidth]);\n\n  // =========================== Ref ============================\n  React.useImperativeHandle(ref, function () {\n    var _listRef$current2;\n    var obj = {\n      scrollTo: function scrollTo(config) {\n        var _listRef$current;\n        (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(config);\n      },\n      nativeElement: (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 ? void 0 : _listRef$current2.nativeElement\n    };\n    Object.defineProperty(obj, 'scrollLeft', {\n      get: function get() {\n        var _listRef$current3;\n        return ((_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 ? void 0 : _listRef$current3.getScrollInfo().x) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current4;\n        (_listRef$current4 = listRef.current) === null || _listRef$current4 === void 0 || _listRef$current4.scrollTo({\n          left: value\n        });\n      }\n    });\n\n    // https://github.com/ant-design/ant-design/issues/54734\n    Object.defineProperty(obj, 'scrollTop', {\n      get: function get() {\n        var _listRef$current5;\n        return ((_listRef$current5 = listRef.current) === null || _listRef$current5 === void 0 ? void 0 : _listRef$current5.getScrollInfo().y) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current6;\n        (_listRef$current6 = listRef.current) === null || _listRef$current6 === void 0 || _listRef$current6.scrollTo({\n          top: value\n        });\n      }\n    });\n    return obj;\n  });\n\n  // ======================= Col/Row Span =======================\n  var getRowSpan = function getRowSpan(column, index) {\n    var _flattenData$index;\n    var record = (_flattenData$index = flattenData[index]) === null || _flattenData$index === void 0 ? void 0 : _flattenData$index.record;\n    var onCell = column.onCell;\n    if (onCell) {\n      var _cellProps$rowSpan;\n      var cellProps = onCell(record, index);\n      return (_cellProps$rowSpan = cellProps === null || cellProps === void 0 ? void 0 : cellProps.rowSpan) !== null && _cellProps$rowSpan !== void 0 ? _cellProps$rowSpan : 1;\n    }\n    return 1;\n  };\n  var extraRender = function extraRender(info) {\n    var start = info.start,\n      end = info.end,\n      getSize = info.getSize,\n      offsetY = info.offsetY;\n\n    // Do nothing if no data\n    if (end < 0) {\n      return null;\n    }\n\n    // Find first rowSpan column\n    var firstRowSpanColumns = flattenColumns.filter(\n    // rowSpan is 0\n    function (column) {\n      return getRowSpan(column, start) === 0;\n    });\n    var startIndex = start;\n    var _loop = function _loop(i) {\n      firstRowSpanColumns = firstRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, i) === 0;\n      });\n      if (!firstRowSpanColumns.length) {\n        startIndex = i;\n        return 1; // break\n      }\n    };\n    for (var i = start; i >= 0; i -= 1) {\n      if (_loop(i)) break;\n    }\n\n    // Find last rowSpan column\n    var lastRowSpanColumns = flattenColumns.filter(\n    // rowSpan is not 1\n    function (column) {\n      return getRowSpan(column, end) !== 1;\n    });\n    var endIndex = end;\n    var _loop2 = function _loop2(_i) {\n      lastRowSpanColumns = lastRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, _i) !== 1;\n      });\n      if (!lastRowSpanColumns.length) {\n        endIndex = Math.max(_i - 1, end);\n        return 1; // break\n      }\n    };\n    for (var _i = end; _i < flattenData.length; _i += 1) {\n      if (_loop2(_i)) break;\n    }\n\n    // Collect the line who has rowSpan\n    var spanLines = [];\n    var _loop3 = function _loop3(_i2) {\n      var item = flattenData[_i2];\n\n      // This code will never reach, just incase\n      if (!item) {\n        return 1; // continue\n      }\n      if (flattenColumns.some(function (column) {\n        return getRowSpan(column, _i2) > 1;\n      })) {\n        spanLines.push(_i2);\n      }\n    };\n    for (var _i2 = startIndex; _i2 <= endIndex; _i2 += 1) {\n      if (_loop3(_i2)) continue;\n    }\n\n    // Patch extra line on the page\n    var nodes = spanLines.map(function (index) {\n      var item = flattenData[index];\n      var rowKey = getRowKey(item.record, index);\n      var getHeight = function getHeight(rowSpan) {\n        var endItemIndex = index + rowSpan - 1;\n        var endItemKey = getRowKey(flattenData[endItemIndex].record, endItemIndex);\n        var sizeInfo = getSize(rowKey, endItemKey);\n        return sizeInfo.bottom - sizeInfo.top;\n      };\n      var sizeInfo = getSize(rowKey);\n      return /*#__PURE__*/React.createElement(BodyLine, {\n        key: index,\n        data: item,\n        rowKey: rowKey,\n        index: index,\n        style: {\n          top: -offsetY + sizeInfo.top\n        },\n        extra: true,\n        getHeight: getHeight\n      });\n    });\n    return nodes;\n  };\n\n  // ========================= Context ==========================\n  var gridContext = React.useMemo(function () {\n    return {\n      columnsOffset: columnsOffset\n    };\n  }, [columnsOffset]);\n\n  // ========================== Render ==========================\n  var tblPrefixCls = \"\".concat(prefixCls, \"-tbody\");\n\n  // default 'div' in rc-virtual-list\n  var wrapperComponent = getComponent(['body', 'wrapper']);\n\n  // ========================== Sticky Scroll Bar ==========================\n  var horizontalScrollBarStyle = {};\n  if (sticky) {\n    horizontalScrollBarStyle.position = 'sticky';\n    horizontalScrollBarStyle.bottom = 0;\n    if (_typeof(sticky) === 'object' && sticky.offsetScroll) {\n      horizontalScrollBarStyle.bottom = sticky.offsetScroll;\n    }\n  }\n  return /*#__PURE__*/React.createElement(GridContext.Provider, {\n    value: gridContext\n  }, /*#__PURE__*/React.createElement(VirtualList, {\n    fullHeight: false,\n    ref: listRef,\n    prefixCls: \"\".concat(tblPrefixCls, \"-virtual\"),\n    styles: {\n      horizontalScrollBar: horizontalScrollBarStyle\n    },\n    className: tblPrefixCls,\n    height: scrollY,\n    itemHeight: listItemHeight || 24,\n    data: flattenData,\n    itemKey: function itemKey(item) {\n      return getRowKey(item.record);\n    },\n    component: wrapperComponent,\n    scrollWidth: scrollX,\n    direction: direction,\n    onVirtualScroll: function onVirtualScroll(_ref4) {\n      var _listRef$current7;\n      var x = _ref4.x;\n      onScroll({\n        currentTarget: (_listRef$current7 = listRef.current) === null || _listRef$current7 === void 0 ? void 0 : _listRef$current7.nativeElement,\n        scrollLeft: x\n      });\n    },\n    onScroll: onTablePropScroll,\n    extraRender: extraRender\n  }, function (item, index, itemProps) {\n    var rowKey = getRowKey(item.record, index);\n    return /*#__PURE__*/React.createElement(BodyLine, {\n      data: item,\n      rowKey: rowKey,\n      index: index,\n      style: itemProps.style\n    });\n  }));\n});\nvar ResponseGrid = responseImmutable(Grid);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseGrid.displayName = 'ResponseGrid';\n}\nexport default ResponseGrid;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,WAAW,EAAEC,aAAa,QAAQ,WAAW;AACtD,IAAIC,IAAI,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;EAC3B,IAAIC,WAAW,GAAGf,UAAU,CAACG,YAAY,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACtKa,cAAc,GAAGD,WAAW,CAACC,cAAc;IAC3CC,cAAc,GAAGF,WAAW,CAACE,cAAc;IAC3CC,SAAS,GAAGH,WAAW,CAACG,SAAS;IACjCC,YAAY,GAAGJ,WAAW,CAACI,YAAY;IACvCC,SAAS,GAAGL,WAAW,CAACK,SAAS;IACjCC,kBAAkB,GAAGN,WAAW,CAACM,kBAAkB;IACnDC,OAAO,GAAGP,WAAW,CAACO,OAAO;IAC7BC,SAAS,GAAGR,WAAW,CAACQ,SAAS;EACnC,IAAIC,YAAY,GAAGxB,UAAU,CAACQ,aAAa,CAAC;IAC1CiB,MAAM,GAAGD,YAAY,CAACC,MAAM;IAC5BC,OAAO,GAAGF,YAAY,CAACE,OAAO;IAC9BC,cAAc,GAAGH,YAAY,CAACG,cAAc;IAC5CC,YAAY,GAAGJ,YAAY,CAACI,YAAY;IACxCC,iBAAiB,GAAGL,YAAY,CAACV,QAAQ;;EAE3C;EACA,IAAIgB,OAAO,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,CAAC;;EAE5B;EACA,IAAIC,WAAW,GAAG3B,iBAAiB,CAACQ,IAAI,EAAEQ,kBAAkB,EAAEF,YAAY,EAAED,SAAS,CAAC;;EAEtF;EACA,IAAIe,YAAY,GAAG/B,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC3C,IAAIC,KAAK,GAAG,CAAC;IACb,OAAOnB,cAAc,CAACoB,GAAG,CAAC,UAAUC,IAAI,EAAE;MACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QACpBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;QACxBC,GAAG,GAAGH,IAAI,CAACG,GAAG;MAChB,IAAIC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,IAAI,CAAC,EAAEC,QAAQ,IAAI,CAAC,CAAC;MACpDJ,KAAK,IAAIM,UAAU;MACnB,OAAO,CAACD,GAAG,EAAEC,UAAU,EAAEN,KAAK,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,cAAc,CAAC,CAAC;EACpB,IAAI4B,aAAa,GAAG1C,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC5C,OAAOD,YAAY,CAACG,GAAG,CAAC,UAAUS,QAAQ,EAAE;MAC1C,OAAOA,QAAQ,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,YAAY,CAAC,CAAC;EAClB/B,KAAK,CAAC4C,SAAS,CAAC,YAAY;IAC1Bb,YAAY,CAACc,OAAO,CAAC,UAAUC,KAAK,EAAE;MACpC,IAAIC,KAAK,GAAGlD,cAAc,CAACiD,KAAK,EAAE,CAAC,CAAC;QAClCR,GAAG,GAAGS,KAAK,CAAC,CAAC,CAAC;QACdX,KAAK,GAAGW,KAAK,CAAC,CAAC,CAAC;MAClBhC,cAAc,CAACuB,GAAG,EAAEF,KAAK,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;;EAElB;EACA/B,KAAK,CAACgD,mBAAmB,CAACtC,GAAG,EAAE,YAAY;IACzC,IAAIuC,iBAAiB;IACrB,IAAIC,GAAG,GAAG;MACRC,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClC,IAAIC,gBAAgB;QACpB,CAACA,gBAAgB,GAAGzB,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACF,QAAQ,CAACC,MAAM,CAAC;MACnH,CAAC;MACDG,aAAa,EAAE,CAACN,iBAAiB,GAAGrB,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAIL,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACM;IAC7H,CAAC;IACDC,MAAM,CAACC,cAAc,CAACP,GAAG,EAAE,YAAY,EAAE;MACvCQ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB,IAAIC,iBAAiB;QACrB,OAAO,CAAC,CAACA,iBAAiB,GAAG/B,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAIK,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACC,aAAa,CAAC,CAAC,CAACC,CAAC,KAAK,CAAC;MAC7I,CAAC;MACDC,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;QACvB,IAAIC,iBAAiB;QACrB,CAACA,iBAAiB,GAAGpC,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAIU,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACb,QAAQ,CAAC;UAC3Gc,IAAI,EAAEF;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACAP,MAAM,CAACC,cAAc,CAACP,GAAG,EAAE,WAAW,EAAE;MACtCQ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB,IAAIQ,iBAAiB;QACrB,OAAO,CAAC,CAACA,iBAAiB,GAAGtC,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAIY,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACN,aAAa,CAAC,CAAC,CAACO,CAAC,KAAK,CAAC;MAC7I,CAAC;MACDL,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;QACvB,IAAIK,iBAAiB;QACrB,CAACA,iBAAiB,GAAGxC,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAIc,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACjB,QAAQ,CAAC;UAC3GkB,GAAG,EAAEN;QACP,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOb,GAAG;EACZ,CAAC,CAAC;;EAEF;EACA,IAAIoB,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAClD,IAAIC,kBAAkB;IACtB,IAAIC,MAAM,GAAG,CAACD,kBAAkB,GAAG3C,WAAW,CAAC0C,KAAK,CAAC,MAAM,IAAI,IAAIC,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,MAAM;IACrI,IAAIC,MAAM,GAAGJ,MAAM,CAACI,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACV,IAAIC,kBAAkB;MACtB,IAAIC,SAAS,GAAGF,MAAM,CAACD,MAAM,EAAEF,KAAK,CAAC;MACrC,OAAO,CAACI,kBAAkB,GAAGC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,OAAO,MAAM,IAAI,IAAIF,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC1K;IACA,OAAO,CAAC;EACV,CAAC;EACD,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;IAC3C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,GAAG,GAAGF,IAAI,CAACE,GAAG;MACdC,OAAO,GAAGH,IAAI,CAACG,OAAO;MACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;;IAExB;IACA,IAAIF,GAAG,GAAG,CAAC,EAAE;MACX,OAAO,IAAI;IACb;;IAEA;IACA,IAAIG,mBAAmB,GAAGvE,cAAc,CAACwE,MAAM;IAC/C;IACA,UAAUf,MAAM,EAAE;MAChB,OAAOD,UAAU,CAACC,MAAM,EAAEU,KAAK,CAAC,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,IAAIM,UAAU,GAAGN,KAAK;IACtB,IAAIO,KAAK,GAAG,SAASA,KAAKA,CAACC,CAAC,EAAE;MAC5BJ,mBAAmB,GAAGA,mBAAmB,CAACC,MAAM,CAAC,UAAUf,MAAM,EAAE;QACjE,OAAOD,UAAU,CAACC,MAAM,EAAEkB,CAAC,CAAC,KAAK,CAAC;MACpC,CAAC,CAAC;MACF,IAAI,CAACJ,mBAAmB,CAACK,MAAM,EAAE;QAC/BH,UAAU,GAAGE,CAAC;QACd,OAAO,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,KAAK,IAAIA,CAAC,GAAGR,KAAK,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAClC,IAAID,KAAK,CAACC,CAAC,CAAC,EAAE;IAChB;;IAEA;IACA,IAAIE,kBAAkB,GAAG7E,cAAc,CAACwE,MAAM;IAC9C;IACA,UAAUf,MAAM,EAAE;MAChB,OAAOD,UAAU,CAACC,MAAM,EAAEW,GAAG,CAAC,KAAK,CAAC;IACtC,CAAC,CAAC;IACF,IAAIU,QAAQ,GAAGV,GAAG;IAClB,IAAIW,MAAM,GAAG,SAASA,MAAMA,CAACC,EAAE,EAAE;MAC/BH,kBAAkB,GAAGA,kBAAkB,CAACL,MAAM,CAAC,UAAUf,MAAM,EAAE;QAC/D,OAAOD,UAAU,CAACC,MAAM,EAAEuB,EAAE,CAAC,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,IAAI,CAACH,kBAAkB,CAACD,MAAM,EAAE;QAC9BE,QAAQ,GAAGpD,IAAI,CAACC,GAAG,CAACqD,EAAE,GAAG,CAAC,EAAEZ,GAAG,CAAC;QAChC,OAAO,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,KAAK,IAAIY,EAAE,GAAGZ,GAAG,EAAEY,EAAE,GAAGhE,WAAW,CAAC4D,MAAM,EAAEI,EAAE,IAAI,CAAC,EAAE;MACnD,IAAID,MAAM,CAACC,EAAE,CAAC,EAAE;IAClB;;IAEA;IACA,IAAIC,SAAS,GAAG,EAAE;IAClB,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;MAChC,IAAIC,IAAI,GAAGpE,WAAW,CAACmE,GAAG,CAAC;;MAE3B;MACA,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,CAAC,CAAC,CAAC;MACZ;MACA,IAAIpF,cAAc,CAACqF,IAAI,CAAC,UAAU5B,MAAM,EAAE;QACxC,OAAOD,UAAU,CAACC,MAAM,EAAE0B,GAAG,CAAC,GAAG,CAAC;MACpC,CAAC,CAAC,EAAE;QACFF,SAAS,CAACK,IAAI,CAACH,GAAG,CAAC;MACrB;IACF,CAAC;IACD,KAAK,IAAIA,GAAG,GAAGV,UAAU,EAAEU,GAAG,IAAIL,QAAQ,EAAEK,GAAG,IAAI,CAAC,EAAE;MACpD,IAAID,MAAM,CAACC,GAAG,CAAC,EAAE;IACnB;;IAEA;IACA,IAAII,KAAK,GAAGN,SAAS,CAAC7D,GAAG,CAAC,UAAUsC,KAAK,EAAE;MACzC,IAAI0B,IAAI,GAAGpE,WAAW,CAAC0C,KAAK,CAAC;MAC7B,IAAI8B,MAAM,GAAGtF,SAAS,CAACkF,IAAI,CAACxB,MAAM,EAAEF,KAAK,CAAC;MAC1C,IAAI+B,SAAS,GAAG,SAASA,SAASA,CAACzB,OAAO,EAAE;QAC1C,IAAI0B,YAAY,GAAGhC,KAAK,GAAGM,OAAO,GAAG,CAAC;QACtC,IAAI2B,UAAU,GAAGzF,SAAS,CAACc,WAAW,CAAC0E,YAAY,CAAC,CAAC9B,MAAM,EAAE8B,YAAY,CAAC;QAC1E,IAAIE,QAAQ,GAAGvB,OAAO,CAACmB,MAAM,EAAEG,UAAU,CAAC;QAC1C,OAAOC,QAAQ,CAACC,MAAM,GAAGD,QAAQ,CAACrC,GAAG;MACvC,CAAC;MACD,IAAIqC,QAAQ,GAAGvB,OAAO,CAACmB,MAAM,CAAC;MAC9B,OAAO,aAAatG,KAAK,CAAC4G,aAAa,CAACxG,QAAQ,EAAE;QAChDkC,GAAG,EAAEkC,KAAK;QACV7D,IAAI,EAAEuF,IAAI;QACVI,MAAM,EAAEA,MAAM;QACd9B,KAAK,EAAEA,KAAK;QACZqC,KAAK,EAAE;UACLxC,GAAG,EAAE,CAACe,OAAO,GAAGsB,QAAQ,CAACrC;QAC3B,CAAC;QACDyC,KAAK,EAAE,IAAI;QACXP,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOF,KAAK;EACd,CAAC;;EAED;EACA,IAAIU,WAAW,GAAG/G,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC1C,OAAO;MACLU,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAIsE,YAAY,GAAG,EAAE,CAACC,MAAM,CAAC/F,SAAS,EAAE,QAAQ,CAAC;;EAEjD;EACA,IAAIgG,gBAAgB,GAAGxF,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;EAExD;EACA,IAAIyF,wBAAwB,GAAG,CAAC,CAAC;EACjC,IAAI5F,MAAM,EAAE;IACV4F,wBAAwB,CAACC,QAAQ,GAAG,QAAQ;IAC5CD,wBAAwB,CAACR,MAAM,GAAG,CAAC;IACnC,IAAI/G,OAAO,CAAC2B,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAAC8F,YAAY,EAAE;MACvDF,wBAAwB,CAACR,MAAM,GAAGpF,MAAM,CAAC8F,YAAY;IACvD;EACF;EACA,OAAO,aAAarH,KAAK,CAAC4G,aAAa,CAACvG,WAAW,CAACiH,QAAQ,EAAE;IAC5DvD,KAAK,EAAEgD;EACT,CAAC,EAAE,aAAa/G,KAAK,CAAC4G,aAAa,CAAC7G,WAAW,EAAE;IAC/CwH,UAAU,EAAE,KAAK;IACjB7G,GAAG,EAAEkB,OAAO;IACZV,SAAS,EAAE,EAAE,CAAC+F,MAAM,CAACD,YAAY,EAAE,UAAU,CAAC;IAC9CQ,MAAM,EAAE;MACNC,mBAAmB,EAAEN;IACvB,CAAC;IACDO,SAAS,EAAEV,YAAY;IACvBW,MAAM,EAAEnG,OAAO;IACfoG,UAAU,EAAEnG,cAAc,IAAI,EAAE;IAChCd,IAAI,EAAEmB,WAAW;IACjB+F,OAAO,EAAE,SAASA,OAAOA,CAAC3B,IAAI,EAAE;MAC9B,OAAOlF,SAAS,CAACkF,IAAI,CAACxB,MAAM,CAAC;IAC/B,CAAC;IACDoD,SAAS,EAAEZ,gBAAgB;IAC3Ba,WAAW,EAAE3G,OAAO;IACpBC,SAAS,EAAEA,SAAS;IACpB2G,eAAe,EAAE,SAASA,eAAeA,CAACC,KAAK,EAAE;MAC/C,IAAIC,iBAAiB;MACrB,IAAIrE,CAAC,GAAGoE,KAAK,CAACpE,CAAC;MACfjD,QAAQ,CAAC;QACPuH,aAAa,EAAE,CAACD,iBAAiB,GAAGtG,OAAO,CAAC0B,OAAO,MAAM,IAAI,IAAI4E,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC3E,aAAa;QACxI6E,UAAU,EAAEvE;MACd,CAAC,CAAC;IACJ,CAAC;IACDjD,QAAQ,EAAEe,iBAAiB;IAC3BoD,WAAW,EAAEA;EACf,CAAC,EAAE,UAAUmB,IAAI,EAAE1B,KAAK,EAAE6D,SAAS,EAAE;IACnC,IAAI/B,MAAM,GAAGtF,SAAS,CAACkF,IAAI,CAACxB,MAAM,EAAEF,KAAK,CAAC;IAC1C,OAAO,aAAaxE,KAAK,CAAC4G,aAAa,CAACxG,QAAQ,EAAE;MAChDO,IAAI,EAAEuF,IAAI;MACVI,MAAM,EAAEA,MAAM;MACd9B,KAAK,EAAEA,KAAK;MACZqC,KAAK,EAAEwB,SAAS,CAACxB;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIyB,YAAY,GAAGpI,iBAAiB,CAACK,IAAI,CAAC;AAC1C,IAAIgI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,YAAY,CAACI,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}