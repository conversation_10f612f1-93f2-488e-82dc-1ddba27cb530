{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Sync value with state.\n * This should only used for internal which not affect outside calculation.\n * Since it's not safe for suspense.\n */\nexport default function useSyncState(defaultValue, controlledValue) {\n  var valueRef = React.useRef(defaultValue);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  var getter = function getter(useControlledValueFirst) {\n    return useControlledValueFirst && controlledValue !== undefined ? controlledValue : valueRef.current;\n  };\n  var setter = function setter(nextValue) {\n    valueRef.current = nextValue;\n    forceUpdate({});\n  };\n  return [getter, setter, getter(true)];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useSyncState", "defaultValue", "controlledValue", "valueRef", "useRef", "_React$useState", "useState", "_React$useState2", "forceUpdate", "getter", "useControlledValueFirst", "undefined", "current", "setter", "nextValue"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-picker/es/hooks/useSyncState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Sync value with state.\n * This should only used for internal which not affect outside calculation.\n * Since it's not safe for suspense.\n */\nexport default function useSyncState(defaultValue, controlledValue) {\n  var valueRef = React.useRef(defaultValue);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  var getter = function getter(useControlledValueFirst) {\n    return useControlledValueFirst && controlledValue !== undefined ? controlledValue : valueRef.current;\n  };\n  var setter = function setter(nextValue) {\n    valueRef.current = nextValue;\n    forceUpdate({});\n  };\n  return [getter, setter, getter(true)];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,eAAe,EAAE;EAClE,IAAIC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACH,YAAY,CAAC;EACzC,IAAII,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIE,MAAM,GAAG,SAASA,MAAMA,CAACC,uBAAuB,EAAE;IACpD,OAAOA,uBAAuB,IAAIR,eAAe,KAAKS,SAAS,GAAGT,eAAe,GAAGC,QAAQ,CAACS,OAAO;EACtG,CAAC;EACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,SAAS,EAAE;IACtCX,QAAQ,CAACS,OAAO,GAAGE,SAAS;IAC5BN,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,OAAO,CAACC,MAAM,EAAEI,MAAM,EAAEJ,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}