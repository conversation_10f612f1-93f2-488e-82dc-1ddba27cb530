{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"type\"],\n  _excluded2 = [\"onClear\"];\nimport * as React from 'react';\nimport PickerContext from \"../context\";\nexport default function Icon(props) {\n  var icon = props.icon,\n    type = props.type,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls;\n  return icon ? /*#__PURE__*/React.createElement(\"span\", _extends({\n    className: \"\".concat(prefixCls, \"-\").concat(type)\n  }, restProps), icon) : null;\n}\nexport function ClearIcon(_ref) {\n  var onClear = _ref.onClear,\n    restProps = _objectWithoutProperties(_ref, _excluded2);\n  return /*#__PURE__*/React.createElement(Icon, _extends({}, restProps, {\n    type: \"clear\",\n    role: \"button\",\n    onMouseDown: function onMouseDown(e) {\n      e.preventDefault();\n    },\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onClear();\n    }\n  }));\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "<PERSON>er<PERSON>ontext", "Icon", "props", "icon", "type", "restProps", "_React$useContext", "useContext", "prefixCls", "createElement", "className", "concat", "ClearIcon", "_ref", "onClear", "role", "onMouseDown", "e", "preventDefault", "onClick", "stopPropagation"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-picker/es/PickerInput/Selector/Icon.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"type\"],\n  _excluded2 = [\"onClear\"];\nimport * as React from 'react';\nimport PickerContext from \"../context\";\nexport default function Icon(props) {\n  var icon = props.icon,\n    type = props.type,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls;\n  return icon ? /*#__PURE__*/React.createElement(\"span\", _extends({\n    className: \"\".concat(prefixCls, \"-\").concat(type)\n  }, restProps), icon) : null;\n}\nexport function ClearIcon(_ref) {\n  var onClear = _ref.onClear,\n    restProps = _objectWithoutProperties(_ref, _excluded2);\n  return /*#__PURE__*/React.createElement(Icon, _extends({}, restProps, {\n    type: \"clear\",\n    role: \"button\",\n    onMouseDown: function onMouseDown(e) {\n      e.preventDefault();\n    },\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onClear();\n    }\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;EAC9BC,UAAU,GAAG,CAAC,SAAS,CAAC;AAC1B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,eAAe,SAASC,IAAIA,CAACC,KAAK,EAAE;EAClC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACjBC,SAAS,GAAGT,wBAAwB,CAACM,KAAK,EAAEL,SAAS,CAAC;EACxD,IAAIS,iBAAiB,GAAGP,KAAK,CAACQ,UAAU,CAACP,aAAa,CAAC;IACrDQ,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,OAAOL,IAAI,GAAG,aAAaJ,KAAK,CAACU,aAAa,CAAC,MAAM,EAAEd,QAAQ,CAAC;IAC9De,SAAS,EAAE,EAAE,CAACC,MAAM,CAACH,SAAS,EAAE,GAAG,CAAC,CAACG,MAAM,CAACP,IAAI;EAClD,CAAC,EAAEC,SAAS,CAAC,EAAEF,IAAI,CAAC,GAAG,IAAI;AAC7B;AACA,OAAO,SAASS,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACxBT,SAAS,GAAGT,wBAAwB,CAACiB,IAAI,EAAEf,UAAU,CAAC;EACxD,OAAO,aAAaC,KAAK,CAACU,aAAa,CAACR,IAAI,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEU,SAAS,EAAE;IACpED,IAAI,EAAE,OAAO;IACbW,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,SAASA,WAAWA,CAACC,CAAC,EAAE;MACnCA,CAAC,CAACC,cAAc,CAAC,CAAC;IACpB,CAAC;IACDC,OAAO,EAAE,SAASA,OAAOA,CAACF,CAAC,EAAE;MAC3BA,CAAC,CAACG,eAAe,CAAC,CAAC;MACnBN,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}