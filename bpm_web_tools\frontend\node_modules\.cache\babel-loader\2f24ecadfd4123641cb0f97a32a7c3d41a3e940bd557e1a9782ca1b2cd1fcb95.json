{"ast": null, "code": "import rules from \"../rule/index\";\nvar array = function array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default array;", "map": {"version": 3, "names": ["rules", "array", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "undefined", "type", "range"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/@rc-component/async-validator/es/validator/array.js"], "sourcesContent": ["import rules from \"../rule/index\";\nvar array = function array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default array;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,eAAe;AACjC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACjE,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAI,CAACL,KAAK,KAAKS,SAAS,IAAIT,KAAK,KAAK,IAAI,KAAK,CAACD,IAAI,CAACO,QAAQ,EAAE;MAC7D,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAJ,KAAK,CAACS,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,EAAE,OAAO,CAAC;IAC7D,IAAIH,KAAK,KAAKS,SAAS,IAAIT,KAAK,KAAK,IAAI,EAAE;MACzCH,KAAK,CAACa,IAAI,CAACX,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MAChDN,KAAK,CAACc,KAAK,CAACZ,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACnD;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}