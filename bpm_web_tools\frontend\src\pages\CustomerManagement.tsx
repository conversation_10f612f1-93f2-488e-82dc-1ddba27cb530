import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Space,
  Typography,
  Button,
  Input,
  message,
  Modal,
  Descriptions,
  Tag,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Spin,
  Tooltip
} from 'antd';
import {
  TeamOutlined,
  SearchOutlined,
  EyeOutlined,
  SaveOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FolderOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { customerApi } from '../services/api';

const { Title, Text } = Typography;
const { Search } = Input;

interface CustomerData {
  company_id: string;
  company_name: string;
  data_source: string;
  folder_path: string;
  file_count: number;
  filename: string;
}

interface CustomerDetail {
  company_id: string;
  company_name: string;
  data_source: string;
  folder_path: string;
  files: Array<{
    filename: string;
    raw_content: string;
    structured_data: Record<string, any>;
    source_path: string;
  }>;
}

const CustomerManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<CustomerData[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerDetail | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 載入客戶列表
  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    setLoading(true);
    try {
      const response = await customerApi.getList();
      setCustomers(response.data.data);
    } catch (error) {
      message.error('載入客戶列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 搜尋客戶
  const handleSearch = async (keyword: string) => {
    setSearchKeyword(keyword);
    setLoading(true);
    try {
      const response = await customerApi.search(keyword);
      setCustomers(response.data.data);
      message.success(`找到 ${response.data.data.length} 筆客戶記錄`);
    } catch (error) {
      message.error('搜尋客戶失敗');
    } finally {
      setLoading(false);
    }
  };

  // 查看客戶詳情
  const handleViewDetail = async (companyId: string) => {
    setLoading(true);
    try {
      const response = await customerApi.getDetail(companyId);
      setSelectedCustomer(response.data.data);
      setDetailVisible(true);
    } catch (error) {
      message.error('載入客戶詳情失敗');
    } finally {
      setLoading(false);
    }
  };

  // 儲存客戶到正式區
  const handleSave = async (companyId: string, companyName: string) => {
    try {
      await customerApi.save(companyId);
      message.success(`客戶 ${companyName} 已儲存到正式區`);
      loadCustomers(); // 重新載入列表
    } catch (error) {
      message.error('儲存客戶失敗');
    }
  };

  // 刪除客戶
  const handleDelete = async (companyId: string, companyName: string) => {
    try {
      await customerApi.delete(companyId);
      message.success(`客戶 ${companyName} 已刪除`);
      loadCustomers(); // 重新載入列表
    } catch (error) {
      message.error('刪除客戶失敗');
    }
  };

  // 表格欄位定義
  const columns = [
    {
      title: '公司 ID',
      dataIndex: 'company_id',
      key: 'company_id',
      width: 120,
      render: (text: string) => <Text code>{text}</Text>,
    },
    {
      title: '公司名稱',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 200,
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: '資料來源',
      dataIndex: 'data_source',
      key: 'data_source',
      width: 150,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '檔案數量',
      dataIndex: 'file_count',
      key: 'file_count',
      width: 100,
      render: (count: number) => (
        <Space>
          <FileTextOutlined />
          <Text>{count}</Text>
        </Space>
      ),
    },
    {
      title: '資料夾路徑',
      dataIndex: 'folder_path',
      key: 'folder_path',
      width: 300,
      render: (text: string) => (
        <Tooltip title={text}>
          <Space>
            <FolderOutlined />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {text.length > 40 ? `${text.substring(0, 40)}...` : text}
            </Text>
          </Space>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: CustomerData) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record.company_id)}
          >
            查看
          </Button>
          <Button
            type="default"
            size="small"
            icon={<SaveOutlined />}
            onClick={() => handleSave(record.company_id, record.company_name)}
          >
            儲存
          </Button>
          <Popconfirm
            title="確定要刪除這個客戶嗎？"
            description="此操作將刪除臨時區和正式區的所有相關資料"
            onConfirm={() => handleDelete(record.company_id, record.company_name)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <TeamOutlined /> 客戶連線管理工具
      </Title>

      {/* 統計資訊 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="總客戶數"
              value={customers.length}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="總檔案數"
              value={customers.reduce((sum, customer) => sum + customer.file_count, 0)}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="平均檔案數"
              value={customers.length > 0 ? Math.round(customers.reduce((sum, customer) => sum + customer.file_count, 0) / customers.length) : 0}
              prefix={<FolderOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜尋和操作 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={16} md={18}>
            <Search
              placeholder="搜尋公司名稱或 ID"
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
              onChange={(e) => {
                if (!e.target.value) {
                  loadCustomers(); // 清空搜尋時重新載入全部
                }
              }}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Button
              type="default"
              icon={<ReloadOutlined />}
              size="large"
              onClick={loadCustomers}
              loading={loading}
              style={{ width: '100%' }}
            >
              重新載入
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 客戶列表 */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={customers}
            rowKey="company_id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 筆記錄`,
            }}
            scroll={{ x: 1200 }}
          />
        </Spin>
      </Card>

      {/* 客戶詳情 Modal */}
      <Modal
        title={`客戶詳情 - ${selectedCustomer?.company_name}`}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            關閉
          </Button>,
          <Button
            key="save"
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              if (selectedCustomer) {
                handleSave(selectedCustomer.company_id, selectedCustomer.company_name);
                setDetailVisible(false);
              }
            }}
          >
            儲存到正式區
          </Button>,
        ]}
        width={1000}
      >
        {selectedCustomer && (
          <div>
            <Descriptions bordered column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="公司 ID">
                <Text code>{selectedCustomer.company_id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="公司名稱">
                <Text strong>{selectedCustomer.company_name}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="資料來源">
                <Tag color="blue">{selectedCustomer.data_source}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="檔案數量">
                {selectedCustomer.files.length}
              </Descriptions.Item>
              <Descriptions.Item label="資料夾路徑" span={2}>
                <Text type="secondary">{selectedCustomer.folder_path}</Text>
              </Descriptions.Item>
            </Descriptions>

            {selectedCustomer.files.map((file, index) => (
              <Card key={index} size="small" style={{ marginBottom: 16 }}>
                <Title level={5}>{file.filename}</Title>
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="檔案路徑">
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {file.source_path}
                    </Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="結構化資料">
                    <div style={{ maxHeight: 200, overflow: 'auto' }}>
                      {Object.entries(file.structured_data).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: 4 }}>
                          <Text strong>{key}: </Text>
                          <Text>{String(value)}</Text>
                        </div>
                      ))}
                    </div>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            ))}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CustomerManagement;
