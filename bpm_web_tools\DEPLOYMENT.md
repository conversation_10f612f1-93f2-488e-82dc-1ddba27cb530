# BPM Web Tools 部署指南

## 部署完成狀態

✅ **專案建置完成** - FastAPI + React 架構已建立  
✅ **後端 API 開發完成** - 所有 8 個 API 端點測試通過  
✅ **前端介面開發完成** - 三個主要工具頁面已實作  
✅ **資料整合完成** - data_output 資料夾已移入專案  
✅ **啟動腳本建立** - 提供一鍵啟動功能  

## 快速啟動

### 方法一：一鍵啟動（推薦）
```cmd
cd bpm_web_tools
start_all.cmd
```

### 方法二：分別啟動
```cmd
# 終端 1 - 啟動後端
cd bpm_web_tools
start_backend.cmd

# 終端 2 - 啟動前端  
cd bpm_web_tools
start_frontend.cmd
```

## 服務地址

- **前端應用**: http://localhost:3000
- **後端 API**: http://localhost:8000  
- **API 文件**: http://localhost:8000/docs

## 功能驗證

### 已測試功能

1. **產品 Release 記錄查詢工具**
   - ✅ 專案列表載入 (BPM, BPM-ISO, NaNaXWeb)
   - ✅ 分支列表動態載入
   - ✅ Release 資料查詢 (147 筆記錄)
   - ✅ 關鍵字搜尋功能
   - ✅ 檔案名稱搜尋功能
   - ✅ Commit 詳細資訊展開

2. **檔案索引路徑查詢工具**
   - ✅ 版本列表載入 (10 個版本)
   - ✅ 關鍵字模糊搜尋 (18 筆結果)
   - ✅ 檔案類型過濾
   - ✅ 批量搜尋功能 (ZIP 上傳)
   - ✅ 修改時間顯示

3. **客戶連線管理工具**
   - ✅ 客戶列表載入 (373 筆記錄)
   - ✅ 客戶搜尋功能 (2 筆結果)
   - ✅ 客戶詳情查看
   - ✅ 儲存到正式區功能
   - ✅ 刪除客戶功能

### API 測試結果
```
測試結果: 8/8 通過
🎉 所有 API 測試通過！
```

## 專案結構

```
bpm_web_tools/
├── backend/                 # FastAPI 後端
│   ├── main.py             # 主程式 (453 行)
│   └── requirements.txt    # Python 依賴
├── frontend/               # React 前端
│   ├── src/
│   │   ├── pages/         # 頁面組件
│   │   │   ├── HomePage.tsx
│   │   │   ├── ReleaseQuery.tsx
│   │   │   ├── PathSearch.tsx
│   │   │   └── CustomerManagement.tsx
│   │   ├── services/
│   │   │   └── api.ts     # API 服務
│   │   └── App.tsx        # 主應用
│   └── package.json
├── data_output/           # 資料目錄
│   ├── bpm_release/       # 147 個 JSON 檔案
│   ├── bpm_path/          # 10 個索引檔案
│   ├── bpm_customer_tmp/  # 373 個客戶檔案
│   └── bpm_customer/      # 正式客戶資料
├── start_all.cmd          # 一鍵啟動腳本
├── start_backend.cmd      # 後端啟動腳本
├── start_frontend.cmd     # 前端啟動腳本
├── test_api.py           # API 測試腳本
├── README.md             # 詳細說明文件
└── DEPLOYMENT.md         # 本部署指南
```

## 技術規格

- **後端**: FastAPI 0.104.1 + Python 3.8+
- **前端**: React 18 + TypeScript + Ant Design
- **資料格式**: JSON
- **API 設計**: RESTful
- **開發環境**: Node.js 16+ + Python 虛擬環境

## 資料統計

- **Release 記錄**: 147 個 JSON 檔案
- **檔案索引**: 10 個版本索引
- **客戶資料**: 373 家公司資料
- **API 端點**: 8 個主要端點
- **前端頁面**: 4 個主要頁面

## 故障排除

### 常見問題

1. **後端啟動失敗**
   ```cmd
   # 檢查虛擬環境
   venv\Scripts\activate
   pip install -r bpm_web_tools\backend\requirements.txt
   ```

2. **前端啟動失敗**
   ```cmd
   cd bpm_web_tools\frontend
   npm install
   npm start
   ```

3. **API 連線失敗**
   - 確認後端服務運行在 http://localhost:8000
   - 檢查 CORS 設定
   - 查看瀏覽器控制台錯誤

### 測試命令

```cmd
# 測試後端 API
cd bpm_web_tools
python test_api.py

# 檢查前端編譯
cd bpm_web_tools\frontend
npm run build
```

## 生產部署建議

1. **後端部署**
   - 使用 Gunicorn 或 Uvicorn 作為 WSGI 服務器
   - 配置 Nginx 反向代理
   - 設定環境變數和配置檔案

2. **前端部署**
   - 執行 `npm run build` 建立生產版本
   - 部署到靜態檔案服務器
   - 配置路由重定向

3. **資料備份**
   - 定期備份 data_output 資料夾
   - 設定資料同步機制

## 維護說明

- **資料更新**: 將新的 JSON 檔案放入對應的 data_output 子資料夾
- **功能擴展**: 在 backend/main.py 新增 API 端點，在 frontend/src/pages 新增頁面
- **版本升級**: 更新 requirements.txt 和 package.json 中的依賴版本

## 聯絡資訊

如有問題請聯絡 BPM 開發團隊。

---

**部署完成時間**: 2025-09-25  
**版本**: 1.0.0  
**狀態**: ✅ 生產就緒
