{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getOffset } from \"../util\";\nvar Track = function Track(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    start = props.start,\n    end = props.end,\n    index = props.index,\n    onStartMove = props.onStartMove,\n    replaceCls = props.replaceCls;\n  var _React$useContext = React.useContext(SliderContext),\n    direction = _React$useContext.direction,\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range,\n    classNames = _React$useContext.classNames;\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max);\n\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  };\n\n  // ============================ Render ============================\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n  var className = replaceCls || cls(trackPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(trackPrefixCls, \"-\").concat(index + 1), index !== null && range), \"\".concat(prefixCls, \"-track-draggable\"), onStartMove), classNames.track);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n};\nexport default Track;", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "cls", "React", "SliderContext", "getOffset", "Track", "props", "prefixCls", "style", "start", "end", "index", "onStartMove", "replaceCls", "_React$useContext", "useContext", "direction", "min", "max", "disabled", "range", "classNames", "trackPrefixCls", "concat", "offsetStart", "offsetEnd", "onInternalStartMove", "e", "positionStyle", "right", "width", "bottom", "height", "top", "left", "className", "track", "createElement", "onMouseDown", "onTouchStart"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-slider/es/Tracks/Track.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getOffset } from \"../util\";\nvar Track = function Track(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    start = props.start,\n    end = props.end,\n    index = props.index,\n    onStartMove = props.onStartMove,\n    replaceCls = props.replaceCls;\n  var _React$useContext = React.useContext(SliderContext),\n    direction = _React$useContext.direction,\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range,\n    classNames = _React$useContext.classNames;\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max);\n\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  };\n\n  // ============================ Render ============================\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n  var className = replaceCls || cls(trackPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(trackPrefixCls, \"-\").concat(index + 1), index !== null && range), \"\".concat(prefixCls, \"-track-draggable\"), onStartMove), classNames.track);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n};\nexport default Track;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,SAAS,QAAQ,SAAS;AACnC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,GAAG,GAAGJ,KAAK,CAACI,GAAG;IACfC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,UAAU,GAAGP,KAAK,CAACO,UAAU;EAC/B,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACZ,aAAa,CAAC;IACrDa,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,GAAG,GAAGJ,iBAAiB,CAACI,GAAG;IAC3BC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,KAAK,GAAGN,iBAAiB,CAACM,KAAK;IAC/BC,UAAU,GAAGP,iBAAiB,CAACO,UAAU;EAC3C,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAAChB,SAAS,EAAE,QAAQ,CAAC;EACnD,IAAIiB,WAAW,GAAGpB,SAAS,CAACK,KAAK,EAAEQ,GAAG,EAAEC,GAAG,CAAC;EAC5C,IAAIO,SAAS,GAAGrB,SAAS,CAACM,GAAG,EAAEO,GAAG,EAAEC,GAAG,CAAC;;EAExC;EACA,IAAIQ,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,CAAC,EAAE;IACxD,IAAI,CAACR,QAAQ,IAAIP,WAAW,EAAE;MAC5BA,WAAW,CAACe,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB;EACF,CAAC;;EAED;EACA,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,QAAQZ,SAAS;IACf,KAAK,KAAK;MACRY,aAAa,CAACC,KAAK,GAAG,EAAE,CAACN,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACvDI,aAAa,CAACE,KAAK,GAAG,EAAE,CAACP,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACzE;IACF,KAAK,KAAK;MACRI,aAAa,CAACG,MAAM,GAAG,EAAE,CAACR,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACxDI,aAAa,CAACI,MAAM,GAAG,EAAE,CAACT,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1E;IACF,KAAK,KAAK;MACRI,aAAa,CAACK,GAAG,GAAG,EAAE,CAACV,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACrDI,aAAa,CAACI,MAAM,GAAG,EAAE,CAACT,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1E;IACF;MACEI,aAAa,CAACM,IAAI,GAAG,EAAE,CAACX,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACtDI,aAAa,CAACE,KAAK,GAAG,EAAE,CAACP,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;EAC7E;EACA,IAAIW,SAAS,GAAGtB,UAAU,IAAIZ,GAAG,CAACqB,cAAc,EAAEtB,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuB,MAAM,CAACD,cAAc,EAAE,GAAG,CAAC,CAACC,MAAM,CAACZ,KAAK,GAAG,CAAC,CAAC,EAAEA,KAAK,KAAK,IAAI,IAAIS,KAAK,CAAC,EAAE,EAAE,CAACG,MAAM,CAAChB,SAAS,EAAE,kBAAkB,CAAC,EAAEK,WAAW,CAAC,EAAES,UAAU,CAACe,KAAK,CAAC;EAC3O,OAAO,aAAalC,KAAK,CAACmC,aAAa,CAAC,KAAK,EAAE;IAC7CF,SAAS,EAAEA,SAAS;IACpB3B,KAAK,EAAET,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,aAAa,CAAC,EAAEpB,KAAK,CAAC;IAC7D8B,WAAW,EAAEZ,mBAAmB;IAChCa,YAAY,EAAEb;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,eAAerB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}