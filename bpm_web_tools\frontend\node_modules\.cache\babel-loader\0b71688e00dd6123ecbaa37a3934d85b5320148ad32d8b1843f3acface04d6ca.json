{"ast": null, "code": "import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex > endIndex ? [0, -1] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}", "map": {"version": 3, "names": ["useMemo", "DEFAULT_SIZE", "width", "height", "left", "top", "right", "useVisibleRange", "tabOffsets", "visibleTabContentValue", "transform", "tabContentSizeValue", "addNodeSizeValue", "operationNodeSizeValue", "_ref", "tabs", "tabPosition", "rtl", "char<PERSON><PERSON><PERSON>", "position", "transformSize", "includes", "Math", "abs", "length", "len", "endIndex", "i", "offset", "get", "key", "floor", "startIndex", "_i", "_offset", "map", "tab", "join"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-tabs/es/hooks/useVisibleRange.js"], "sourcesContent": ["import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex > endIndex ? [0, -1] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE;AACT,CAAC;AACD,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAEC,sBAAsB,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,IAAI,EAAE;EAC1J,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,WAAW,GAAGF,IAAI,CAACE,WAAW;IAC9BC,GAAG,GAAGH,IAAI,CAACG,GAAG;EAChB,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EACZ,IAAIC,aAAa;EACjB,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACL,WAAW,CAAC,EAAE;IAC3CE,QAAQ,GAAG,OAAO;IAClBC,QAAQ,GAAGF,GAAG,GAAG,OAAO,GAAG,MAAM;IACjCG,aAAa,GAAGE,IAAI,CAACC,GAAG,CAACb,SAAS,CAAC;EACrC,CAAC,MAAM;IACLQ,QAAQ,GAAG,QAAQ;IACnBC,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAACV,SAAS;EAC5B;EACA,OAAOV,OAAO,CAAC,YAAY;IACzB,IAAI,CAACe,IAAI,CAACS,MAAM,EAAE;MAChB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACf;IACA,IAAIC,GAAG,GAAGV,IAAI,CAACS,MAAM;IACrB,IAAIE,QAAQ,GAAGD,GAAG;IAClB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIC,MAAM,GAAGpB,UAAU,CAACqB,GAAG,CAACd,IAAI,CAACY,CAAC,CAAC,CAACG,GAAG,CAAC,IAAI7B,YAAY;MACxD,IAAIqB,IAAI,CAACS,KAAK,CAACH,MAAM,CAACT,QAAQ,CAAC,GAAGS,MAAM,CAACV,QAAQ,CAAC,CAAC,GAAGI,IAAI,CAACS,KAAK,CAACX,aAAa,GAAGX,sBAAsB,CAAC,EAAE;QACxGiB,QAAQ,GAAGC,CAAC,GAAG,CAAC;QAChB;MACF;IACF;IACA,IAAIK,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIC,EAAE,GAAGR,GAAG,GAAG,CAAC,EAAEQ,EAAE,IAAI,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;MACvC,IAAIC,OAAO,GAAG1B,UAAU,CAACqB,GAAG,CAACd,IAAI,CAACkB,EAAE,CAAC,CAACH,GAAG,CAAC,IAAI7B,YAAY;MAC1D,IAAIiC,OAAO,CAACf,QAAQ,CAAC,GAAGC,aAAa,EAAE;QACrCY,UAAU,GAAGC,EAAE,GAAG,CAAC;QACnB;MACF;IACF;IACA,OAAOD,UAAU,GAAGN,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAACM,UAAU,EAAEN,QAAQ,CAAC;EACjE,CAAC,EAAE,CAAClB,UAAU,EAAEC,sBAAsB,EAAEE,mBAAmB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEO,aAAa,EAAEJ,WAAW,EAAED,IAAI,CAACoB,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzJ,OAAOA,GAAG,CAACN,GAAG;EAChB,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,EAAEpB,GAAG,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}