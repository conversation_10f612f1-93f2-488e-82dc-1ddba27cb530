{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nvar HeaderRow = function HeaderRow(props) {\n  var cells = props.cells,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns,\n    RowComponent = props.rowComponent,\n    CellComponent = props.cellComponent,\n    onHeaderRow = props.onHeaderRow,\n    index = props.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var rowProps;\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction);\n    var additionalProps;\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      scope: column.title ? cell.colSpan > 1 ? 'colgroup' : 'col' : null,\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: CellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  HeaderRow.displayName = 'HeaderRow';\n}\nexport default HeaderRow;", "map": {"version": 3, "names": ["_extends", "React", "Cell", "TableContext", "useContext", "getCellFixedInfo", "getColumnsKey", "HeaderRow", "props", "cells", "stickyOffsets", "flattenColumns", "RowComponent", "rowComponent", "CellComponent", "cellComponent", "onHeaderRow", "index", "_useContext", "prefixCls", "direction", "rowProps", "map", "cell", "column", "columnsKey", "createElement", "cellIndex", "fixedInfo", "colStart", "colEnd", "additionalProps", "onHeaderCell", "scope", "title", "colSpan", "ellipsis", "align", "component", "key", "rowType", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-table/es/Header/HeaderRow.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nvar HeaderRow = function HeaderRow(props) {\n  var cells = props.cells,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns,\n    RowComponent = props.rowComponent,\n    CellComponent = props.cellComponent,\n    onHeaderRow = props.onHeaderRow,\n    index = props.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var rowProps;\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction);\n    var additionalProps;\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      scope: column.title ? cell.colSpan > 1 ? 'colgroup' : 'col' : null,\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: CellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  HeaderRow.displayName = 'HeaderRow';\n}\nexport default HeaderRow;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrBC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,YAAY,GAAGJ,KAAK,CAACK,YAAY;IACjCC,aAAa,GAAGN,KAAK,CAACO,aAAa;IACnCC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,KAAK,GAAGT,KAAK,CAACS,KAAK;EACrB,IAAIC,WAAW,GAAGd,UAAU,CAACD,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACpEgB,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACnC,IAAIC,QAAQ;EACZ,IAAIL,WAAW,EAAE;IACfK,QAAQ,GAAGL,WAAW,CAACP,KAAK,CAACa,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAACC,MAAM;IACpB,CAAC,CAAC,EAAEP,KAAK,CAAC;EACZ;EACA,IAAIQ,UAAU,GAAGnB,aAAa,CAACG,KAAK,CAACa,GAAG,CAAC,UAAUC,IAAI,EAAE;IACvD,OAAOA,IAAI,CAACC,MAAM;EACpB,CAAC,CAAC,CAAC;EACH,OAAO,aAAavB,KAAK,CAACyB,aAAa,CAACd,YAAY,EAAES,QAAQ,EAAEZ,KAAK,CAACa,GAAG,CAAC,UAAUC,IAAI,EAAEI,SAAS,EAAE;IACnG,IAAIH,MAAM,GAAGD,IAAI,CAACC,MAAM;IACxB,IAAII,SAAS,GAAGvB,gBAAgB,CAACkB,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAACO,MAAM,EAAEnB,cAAc,EAAED,aAAa,EAAEU,SAAS,CAAC;IACtG,IAAIW,eAAe;IACnB,IAAIP,MAAM,IAAIA,MAAM,CAACQ,YAAY,EAAE;MACjCD,eAAe,GAAGR,IAAI,CAACC,MAAM,CAACQ,YAAY,CAACR,MAAM,CAAC;IACpD;IACA,OAAO,aAAavB,KAAK,CAACyB,aAAa,CAACxB,IAAI,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEuB,IAAI,EAAE;MAC/DU,KAAK,EAAET,MAAM,CAACU,KAAK,GAAGX,IAAI,CAACY,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,KAAK,GAAG,IAAI;MAClEC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;MACzBC,KAAK,EAAEb,MAAM,CAACa,KAAK;MACnBC,SAAS,EAAExB,aAAa;MACxBK,SAAS,EAAEA,SAAS;MACpBoB,GAAG,EAAEd,UAAU,CAACE,SAAS;IAC3B,CAAC,EAAEC,SAAS,EAAE;MACZG,eAAe,EAAEA,eAAe;MAChCS,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpC,SAAS,CAACqC,WAAW,GAAG,WAAW;AACrC;AACA,eAAerC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}