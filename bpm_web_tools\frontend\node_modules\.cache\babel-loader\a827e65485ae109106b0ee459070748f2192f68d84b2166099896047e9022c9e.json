{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"show\"];\nimport * as React from 'react';\n/**\n * Cut `value` by the `count.max` prop.\n */\nexport function inCountRange(value, countConfig) {\n  if (!countConfig.max) {\n    return true;\n  }\n  var count = countConfig.strategy(value);\n  return count <= countConfig.max;\n}\nexport default function useCount(count, showCount) {\n  return React.useMemo(function () {\n    var mergedConfig = {};\n    if (showCount) {\n      mergedConfig.show = _typeof(showCount) === 'object' && showCount.formatter ? showCount.formatter : !!showCount;\n    }\n    mergedConfig = _objectSpread(_objectSpread({}, mergedConfig), count);\n    var _ref = mergedConfig,\n      show = _ref.show,\n      rest = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, rest), {}, {\n      show: !!show,\n      showFormatter: typeof show === 'function' ? show : undefined,\n      strategy: rest.strategy || function (value) {\n        return value.length;\n      }\n    });\n  }, [count, showCount]);\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_objectSpread", "_typeof", "_excluded", "React", "inCountRange", "value", "countConfig", "max", "count", "strategy", "useCount", "showCount", "useMemo", "mergedConfig", "show", "formatter", "_ref", "rest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "length"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-input/es/hooks/useCount.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"show\"];\nimport * as React from 'react';\n/**\n * Cut `value` by the `count.max` prop.\n */\nexport function inCountRange(value, countConfig) {\n  if (!countConfig.max) {\n    return true;\n  }\n  var count = countConfig.strategy(value);\n  return count <= countConfig.max;\n}\nexport default function useCount(count, showCount) {\n  return React.useMemo(function () {\n    var mergedConfig = {};\n    if (showCount) {\n      mergedConfig.show = _typeof(showCount) === 'object' && showCount.formatter ? showCount.formatter : !!showCount;\n    }\n    mergedConfig = _objectSpread(_objectSpread({}, mergedConfig), count);\n    var _ref = mergedConfig,\n      show = _ref.show,\n      rest = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, rest), {}, {\n      show: !!show,\n      showFormatter: typeof show === 'function' ? show : undefined,\n      strategy: rest.strategy || function (value) {\n        return value.length;\n      }\n    });\n  }, [count, showCount]);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAC/C,IAAI,CAACA,WAAW,CAACC,GAAG,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,GAAGF,WAAW,CAACG,QAAQ,CAACJ,KAAK,CAAC;EACvC,OAAOG,KAAK,IAAIF,WAAW,CAACC,GAAG;AACjC;AACA,eAAe,SAASG,QAAQA,CAACF,KAAK,EAAEG,SAAS,EAAE;EACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,YAAY;IAC/B,IAAIC,YAAY,GAAG,CAAC,CAAC;IACrB,IAAIF,SAAS,EAAE;MACbE,YAAY,CAACC,IAAI,GAAGb,OAAO,CAACU,SAAS,CAAC,KAAK,QAAQ,IAAIA,SAAS,CAACI,SAAS,GAAGJ,SAAS,CAACI,SAAS,GAAG,CAAC,CAACJ,SAAS;IAChH;IACAE,YAAY,GAAGb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,YAAY,CAAC,EAAEL,KAAK,CAAC;IACpE,IAAIQ,IAAI,GAAGH,YAAY;MACrBC,IAAI,GAAGE,IAAI,CAACF,IAAI;MAChBG,IAAI,GAAGlB,wBAAwB,CAACiB,IAAI,EAAEd,SAAS,CAAC;IAClD,OAAOF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDH,IAAI,EAAE,CAAC,CAACA,IAAI;MACZI,aAAa,EAAE,OAAOJ,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGK,SAAS;MAC5DV,QAAQ,EAAEQ,IAAI,CAACR,QAAQ,IAAI,UAAUJ,KAAK,EAAE;QAC1C,OAAOA,KAAK,CAACe,MAAM;MACrB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,KAAK,EAAEG,SAAS,CAAC,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}