{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = new Error().stack;\n  }\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  if (response) {\n    this.response = response;\n    this.status = response.status ? response.status : null;\n  }\n}\nutils.inherits(AxiosError, <PERSON>rror, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: utils.toJSONObject(this.config),\n      code: this.code,\n      status: this.status\n    };\n  }\n});\nconst prototype = AxiosError.prototype;\nconst descriptors = {};\n['ERR_BAD_OPTION_VALUE', 'ERR_BAD_OPTION', 'ECONNABORTED', 'ETIMEDOUT', 'ERR_NETWORK', 'ERR_FR_TOO_MANY_REDIRECTS', 'ERR_DEPRECATED', 'ERR_BAD_RESPONSE', 'ERR_BAD_REQUEST', 'ERR_CANCELED', 'ERR_NOT_SUPPORT', 'ERR_INVALID_URL'\n// eslint-disable-next-line func-names\n].forEach(code => {\n  descriptors[code] = {\n    value: code\n  };\n});\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {\n  value: true\n});\n\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps) => {\n  const axiosError = Object.create(prototype);\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, prop => {\n    return prop !== 'isAxiosError';\n  });\n  const msg = error && error.message ? error.message : 'Error';\n\n  // Prefer explicit code; otherwise copy the low-level error's code (e.g. ECONNREFUSED)\n  const errCode = code == null && error ? error.code : code;\n  AxiosError.call(axiosError, msg, errCode, config, request, response);\n\n  // Chain the original error on the standard field; non-enumerable to avoid JSON noise\n  if (error && axiosError.cause == null) {\n    Object.defineProperty(axiosError, 'cause', {\n      value: error,\n      configurable: true\n    });\n  }\n  axiosError.name = error && error.name || 'Error';\n  customProps && Object.assign(axiosError, customProps);\n  return axiosError;\n};\nexport default AxiosError;", "map": {"version": 3, "names": ["utils", "AxiosError", "message", "code", "config", "request", "response", "Error", "call", "captureStackTrace", "constructor", "stack", "name", "status", "inherits", "toJSON", "description", "number", "fileName", "lineNumber", "columnNumber", "toJSONObject", "prototype", "descriptors", "for<PERSON>ach", "value", "Object", "defineProperties", "defineProperty", "from", "error", "customProps", "axiosError", "create", "toFlatObject", "filter", "obj", "prop", "msg", "errCode", "cause", "configurable", "assign"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/axios/lib/core/AxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = (new Error()).stack;\n  }\n\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  if (response) {\n    this.response = response;\n    this.status = response.status ? response.status : null;\n  }\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: utils.toJSONObject(this.config),\n      code: this.code,\n      status: this.status\n    };\n  }\n});\n\nconst prototype = AxiosError.prototype;\nconst descriptors = {};\n\n[\n  'ERR_BAD_OPTION_VALUE',\n  'ERR_BAD_OPTION',\n  'ECONNABORTED',\n  'ETIMEDOUT',\n  'ERR_NETWORK',\n  'ERR_FR_TOO_MANY_REDIRECTS',\n  'ERR_DEPRECATED',\n  'ERR_BAD_RESPONSE',\n  'ERR_BAD_REQUEST',\n  'ERR_CANCELED',\n  'ERR_NOT_SUPPORT',\n  'ERR_INVALID_URL'\n// eslint-disable-next-line func-names\n].forEach(code => {\n  descriptors[code] = {value: code};\n});\n\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {value: true});\n\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps) => {\n  const axiosError = Object.create(prototype);\n\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, prop => {\n    return prop !== 'isAxiosError';\n  });\n\n  const msg = error && error.message ? error.message : 'Error';\n\n  // Prefer explicit code; otherwise copy the low-level error's code (e.g. ECONNREFUSED)\n  const errCode = code == null && error ? error.code : code;\n  AxiosError.call(axiosError, msg, errCode, config, request, response);\n\n  // Chain the original error on the standard field; non-enumerable to avoid JSON noise\n  if (error && axiosError.cause == null) {\n    Object.defineProperty(axiosError, 'cause', { value: error, configurable: true });\n  }\n\n  axiosError.name = (error && error.name) || 'Error';\n\n  customProps && Object.assign(axiosError, customProps);\n\n  return axiosError;\n};\n\nexport default AxiosError;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC5DC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EAEhB,IAAID,KAAK,CAACE,iBAAiB,EAAE;IAC3BF,KAAK,CAACE,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACC,WAAW,CAAC;EACjD,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,GAAI,IAAIJ,KAAK,CAAC,CAAC,CAAEI,KAAK;EAClC;EAEA,IAAI,CAACT,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACU,IAAI,GAAG,YAAY;EACxBT,IAAI,KAAK,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAC;EAC1BC,MAAM,KAAK,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAC;EAChCC,OAAO,KAAK,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAC;EACnC,IAAIC,QAAQ,EAAE;IACZ,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACO,MAAM,GAAGP,QAAQ,CAACO,MAAM,GAAGP,QAAQ,CAACO,MAAM,GAAG,IAAI;EACxD;AACF;AAEAb,KAAK,CAACc,QAAQ,CAACb,UAAU,EAAEM,KAAK,EAAE;EAChCQ,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO;MACL;MACAb,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBU,IAAI,EAAE,IAAI,CAACA,IAAI;MACf;MACAI,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB;MACAC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BT,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB;MACAP,MAAM,EAAEJ,KAAK,CAACqB,YAAY,CAAC,IAAI,CAACjB,MAAM,CAAC;MACvCD,IAAI,EAAE,IAAI,CAACA,IAAI;MACfU,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;EACH;AACF,CAAC,CAAC;AAEF,MAAMS,SAAS,GAAGrB,UAAU,CAACqB,SAAS;AACtC,MAAMC,WAAW,GAAG,CAAC,CAAC;AAEtB,CACE,sBAAsB,EACtB,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB;AACF;AAAA,CACC,CAACC,OAAO,CAACrB,IAAI,IAAI;EAChBoB,WAAW,CAACpB,IAAI,CAAC,GAAG;IAACsB,KAAK,EAAEtB;EAAI,CAAC;AACnC,CAAC,CAAC;AAEFuB,MAAM,CAACC,gBAAgB,CAAC1B,UAAU,EAAEsB,WAAW,CAAC;AAChDG,MAAM,CAACE,cAAc,CAACN,SAAS,EAAE,cAAc,EAAE;EAACG,KAAK,EAAE;AAAI,CAAC,CAAC;;AAE/D;AACAxB,UAAU,CAAC4B,IAAI,GAAG,CAACC,KAAK,EAAE3B,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEyB,WAAW,KAAK;EACzE,MAAMC,UAAU,GAAGN,MAAM,CAACO,MAAM,CAACX,SAAS,CAAC;EAE3CtB,KAAK,CAACkC,YAAY,CAACJ,KAAK,EAAEE,UAAU,EAAE,SAASG,MAAMA,CAACC,GAAG,EAAE;IACzD,OAAOA,GAAG,KAAK7B,KAAK,CAACe,SAAS;EAChC,CAAC,EAAEe,IAAI,IAAI;IACT,OAAOA,IAAI,KAAK,cAAc;EAChC,CAAC,CAAC;EAEF,MAAMC,GAAG,GAAGR,KAAK,IAAIA,KAAK,CAAC5B,OAAO,GAAG4B,KAAK,CAAC5B,OAAO,GAAG,OAAO;;EAE5D;EACA,MAAMqC,OAAO,GAAGpC,IAAI,IAAI,IAAI,IAAI2B,KAAK,GAAGA,KAAK,CAAC3B,IAAI,GAAGA,IAAI;EACzDF,UAAU,CAACO,IAAI,CAACwB,UAAU,EAAEM,GAAG,EAAEC,OAAO,EAAEnC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC;;EAEpE;EACA,IAAIwB,KAAK,IAAIE,UAAU,CAACQ,KAAK,IAAI,IAAI,EAAE;IACrCd,MAAM,CAACE,cAAc,CAACI,UAAU,EAAE,OAAO,EAAE;MAAEP,KAAK,EAAEK,KAAK;MAAEW,YAAY,EAAE;IAAK,CAAC,CAAC;EAClF;EAEAT,UAAU,CAACpB,IAAI,GAAIkB,KAAK,IAAIA,KAAK,CAAClB,IAAI,IAAK,OAAO;EAElDmB,WAAW,IAAIL,MAAM,CAACgB,MAAM,CAACV,UAAU,EAAED,WAAW,CAAC;EAErD,OAAOC,UAAU;AACnB,CAAC;AAED,eAAe/B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}