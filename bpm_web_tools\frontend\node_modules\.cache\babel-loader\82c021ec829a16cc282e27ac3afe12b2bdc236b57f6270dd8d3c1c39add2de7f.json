{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef, useState } from 'react';\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nexport default function useUpdate(callback) {\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = useRef(0);\n  var callbackRef = useRef();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  useLayoutUpdateEffect(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nexport function useUpdateState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState3 = useState({}),\n    _useState4 = _slicedToArray(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useLayoutUpdateEffect", "useRef", "useState", "useUpdate", "callback", "_useState", "_useState2", "count", "setCount", "effectRef", "callback<PERSON><PERSON>", "current", "_callbackRef$current", "call", "useUpdateState", "defaultState", "batchRef", "_useState3", "_useState4", "forceUpdate", "state", "flushUpdate", "for<PERSON>ach", "updater", "push"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-tabs/es/hooks/useUpdate.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef, useState } from 'react';\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nexport default function useUpdate(callback) {\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = useRef(0);\n  var callbackRef = useRef();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  useLayoutUpdateEffect(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nexport function useUpdateState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState3 = useState({}),\n    _useState4 = _slicedToArray(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,qBAAqB,QAAQ,kCAAkC;AACxE,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;;AAExC;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC1C,IAAIC,SAAS,GAAGH,QAAQ,CAAC,CAAC,CAAC;IACzBI,UAAU,GAAGP,cAAc,CAACM,SAAS,EAAE,CAAC,CAAC;IACzCE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIG,SAAS,GAAGR,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIS,WAAW,GAAGT,MAAM,CAAC,CAAC;EAC1BS,WAAW,CAACC,OAAO,GAAGP,QAAQ;;EAE9B;EACAJ,qBAAqB,CAAC,YAAY;IAChC,IAAIY,oBAAoB;IACxB,CAACA,oBAAoB,GAAGF,WAAW,CAACC,OAAO,MAAM,IAAI,IAAIC,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAACC,IAAI,CAACH,WAAW,CAAC;EACpI,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEX;EACA,OAAO,YAAY;IACjB,IAAIE,SAAS,CAACE,OAAO,KAAKJ,KAAK,EAAE;MAC/B;IACF;IACAE,SAAS,CAACE,OAAO,IAAI,CAAC;IACtBH,QAAQ,CAACC,SAAS,CAACE,OAAO,CAAC;EAC7B,CAAC;AACH;AACA,OAAO,SAASG,cAAcA,CAACC,YAAY,EAAE;EAC3C,IAAIC,QAAQ,GAAGf,MAAM,CAAC,EAAE,CAAC;EACzB,IAAIgB,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3BgB,UAAU,GAAGnB,cAAc,CAACkB,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;EAC7B,IAAIE,KAAK,GAAGnB,MAAM,CAAC,OAAOc,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY,CAAC;EACtF,IAAIM,WAAW,GAAGlB,SAAS,CAAC,YAAY;IACtC,IAAIQ,OAAO,GAAGS,KAAK,CAACT,OAAO;IAC3BK,QAAQ,CAACL,OAAO,CAACW,OAAO,CAAC,UAAUlB,QAAQ,EAAE;MAC3CO,OAAO,GAAGP,QAAQ,CAACO,OAAO,CAAC;IAC7B,CAAC,CAAC;IACFK,QAAQ,CAACL,OAAO,GAAG,EAAE;IACrBS,KAAK,CAACT,OAAO,GAAGA,OAAO;IACvBQ,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,SAASI,OAAOA,CAACnB,QAAQ,EAAE;IACzBY,QAAQ,CAACL,OAAO,CAACa,IAAI,CAACpB,QAAQ,CAAC;IAC/BiB,WAAW,CAAC,CAAC;EACf;EACA,OAAO,CAACD,KAAK,CAACT,OAAO,EAAEY,OAAO,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}