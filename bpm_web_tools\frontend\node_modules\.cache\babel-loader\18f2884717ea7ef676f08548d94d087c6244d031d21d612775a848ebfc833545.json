{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { AggregationColor } from '../../color';\nimport { getGradientPercentColor } from '../../util';\nimport { GradientColorSlider } from '../ColorSlider';\nfunction sortColors(colors) {\n  return _toConsumableArray(colors).sort((a, b) => a.percent - b.percent);\n}\n/**\n * GradientColorBar will auto show when the mode is `gradient`.\n */\nconst GradientColorBar = props => {\n  const {\n    prefixCls,\n    mode,\n    onChange,\n    onChangeComplete,\n    onActive,\n    activeIndex,\n    onGradientDragging,\n    colors\n  } = props;\n  const isGradient = mode === 'gradient';\n  // ============================= Colors =============================\n  const colorList = React.useMemo(() => colors.map(info => ({\n    percent: info.percent,\n    color: info.color.toRgbString()\n  })), [colors]);\n  const values = React.useMemo(() => colorList.map(info => info.percent), [colorList]);\n  // ============================== Drag ==============================\n  const colorsRef = React.useRef(colorList);\n  // Record current colors\n  const onDragStart = ({\n    rawValues,\n    draggingIndex,\n    draggingValue\n  }) => {\n    if (rawValues.length > colorList.length) {\n      // Add new node\n      const newPointColor = getGradientPercentColor(colorList, draggingValue);\n      const nextColors = _toConsumableArray(colorList);\n      nextColors.splice(draggingIndex, 0, {\n        percent: draggingValue,\n        color: newPointColor\n      });\n      colorsRef.current = nextColors;\n    } else {\n      colorsRef.current = colorList;\n    }\n    onGradientDragging(true);\n    onChange(new AggregationColor(sortColors(colorsRef.current)), true);\n  };\n  // Adjust color when dragging\n  const onDragChange = ({\n    deleteIndex,\n    draggingIndex,\n    draggingValue\n  }) => {\n    let nextColors = _toConsumableArray(colorsRef.current);\n    if (deleteIndex !== -1) {\n      nextColors.splice(deleteIndex, 1);\n    } else {\n      nextColors[draggingIndex] = Object.assign(Object.assign({}, nextColors[draggingIndex]), {\n        percent: draggingValue\n      });\n      nextColors = sortColors(nextColors);\n    }\n    onChange(new AggregationColor(nextColors), true);\n  };\n  // ============================== Key ===============================\n  const onKeyDelete = index => {\n    const nextColors = _toConsumableArray(colorList);\n    nextColors.splice(index, 1);\n    const nextColor = new AggregationColor(nextColors);\n    onChange(nextColor);\n    onChangeComplete(nextColor);\n  };\n  // ============================= Change =============================\n  const onInternalChangeComplete = nextValues => {\n    onChangeComplete(new AggregationColor(colorList));\n    // Reset `activeIndex` if out of range\n    if (activeIndex >= nextValues.length) {\n      onActive(nextValues.length - 1);\n    }\n    onGradientDragging(false);\n  };\n  // ============================= Render =============================\n  if (!isGradient) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(GradientColorSlider, {\n    min: 0,\n    max: 100,\n    prefixCls: prefixCls,\n    className: `${prefixCls}-gradient-slider`,\n    colors: colorList,\n    color: null,\n    value: values,\n    range: true,\n    onChangeComplete: onInternalChangeComplete,\n    disabled: false,\n    type: \"gradient\",\n    // Active\n    activeIndex: activeIndex,\n    onActive: onActive,\n    // Drag\n    onDragStart: onDragStart,\n    onDragChange: onDragChange,\n    onKeyDelete: onKeyDelete\n  });\n};\nexport default /*#__PURE__*/React.memo(GradientColorBar);", "map": {"version": 3, "names": ["_toConsumableArray", "React", "AggregationColor", "getGradientPercentColor", "GradientColorSlider", "sortColors", "colors", "sort", "a", "b", "percent", "GradientColorBar", "props", "prefixCls", "mode", "onChange", "onChangeComplete", "onActive", "activeIndex", "onGradientDragging", "isGradient", "colorList", "useMemo", "map", "info", "color", "toRgbString", "values", "colorsRef", "useRef", "onDragStart", "rawValues", "draggingIndex", "draggingValue", "length", "newPointColor", "nextColors", "splice", "current", "onDragChange", "deleteIndex", "Object", "assign", "onKeyDelete", "index", "nextColor", "onInternalChangeComplete", "nextV<PERSON>ues", "createElement", "min", "max", "className", "value", "range", "disabled", "type", "memo"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/antd/es/color-picker/components/PanelPicker/GradientColorBar.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { AggregationColor } from '../../color';\nimport { getGradientPercentColor } from '../../util';\nimport { GradientColorSlider } from '../ColorSlider';\nfunction sortColors(colors) {\n  return _toConsumableArray(colors).sort((a, b) => a.percent - b.percent);\n}\n/**\n * GradientColorBar will auto show when the mode is `gradient`.\n */\nconst GradientColorBar = props => {\n  const {\n    prefixCls,\n    mode,\n    onChange,\n    onChangeComplete,\n    onActive,\n    activeIndex,\n    onGradientDragging,\n    colors\n  } = props;\n  const isGradient = mode === 'gradient';\n  // ============================= Colors =============================\n  const colorList = React.useMemo(() => colors.map(info => ({\n    percent: info.percent,\n    color: info.color.toRgbString()\n  })), [colors]);\n  const values = React.useMemo(() => colorList.map(info => info.percent), [colorList]);\n  // ============================== Drag ==============================\n  const colorsRef = React.useRef(colorList);\n  // Record current colors\n  const onDragStart = ({\n    rawValues,\n    draggingIndex,\n    draggingValue\n  }) => {\n    if (rawValues.length > colorList.length) {\n      // Add new node\n      const newPointColor = getGradientPercentColor(colorList, draggingValue);\n      const nextColors = _toConsumableArray(colorList);\n      nextColors.splice(draggingIndex, 0, {\n        percent: draggingValue,\n        color: newPointColor\n      });\n      colorsRef.current = nextColors;\n    } else {\n      colorsRef.current = colorList;\n    }\n    onGradientDragging(true);\n    onChange(new AggregationColor(sortColors(colorsRef.current)), true);\n  };\n  // Adjust color when dragging\n  const onDragChange = ({\n    deleteIndex,\n    draggingIndex,\n    draggingValue\n  }) => {\n    let nextColors = _toConsumableArray(colorsRef.current);\n    if (deleteIndex !== -1) {\n      nextColors.splice(deleteIndex, 1);\n    } else {\n      nextColors[draggingIndex] = Object.assign(Object.assign({}, nextColors[draggingIndex]), {\n        percent: draggingValue\n      });\n      nextColors = sortColors(nextColors);\n    }\n    onChange(new AggregationColor(nextColors), true);\n  };\n  // ============================== Key ===============================\n  const onKeyDelete = index => {\n    const nextColors = _toConsumableArray(colorList);\n    nextColors.splice(index, 1);\n    const nextColor = new AggregationColor(nextColors);\n    onChange(nextColor);\n    onChangeComplete(nextColor);\n  };\n  // ============================= Change =============================\n  const onInternalChangeComplete = nextValues => {\n    onChangeComplete(new AggregationColor(colorList));\n    // Reset `activeIndex` if out of range\n    if (activeIndex >= nextValues.length) {\n      onActive(nextValues.length - 1);\n    }\n    onGradientDragging(false);\n  };\n  // ============================= Render =============================\n  if (!isGradient) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(GradientColorSlider, {\n    min: 0,\n    max: 100,\n    prefixCls: prefixCls,\n    className: `${prefixCls}-gradient-slider`,\n    colors: colorList,\n    color: null,\n    value: values,\n    range: true,\n    onChangeComplete: onInternalChangeComplete,\n    disabled: false,\n    type: \"gradient\",\n    // Active\n    activeIndex: activeIndex,\n    onActive: onActive,\n    // Drag\n    onDragStart: onDragStart,\n    onDragChange: onDragChange,\n    onKeyDelete: onKeyDelete\n  });\n};\nexport default /*#__PURE__*/React.memo(GradientColorBar);"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,uBAAuB,QAAQ,YAAY;AACpD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAON,kBAAkB,CAACM,MAAM,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,OAAO,GAAGD,CAAC,CAACC,OAAO,CAAC;AACzE;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,gBAAgB;IAChBC,QAAQ;IACRC,WAAW;IACXC,kBAAkB;IAClBb;EACF,CAAC,GAAGM,KAAK;EACT,MAAMQ,UAAU,GAAGN,IAAI,KAAK,UAAU;EACtC;EACA,MAAMO,SAAS,GAAGpB,KAAK,CAACqB,OAAO,CAAC,MAAMhB,MAAM,CAACiB,GAAG,CAACC,IAAI,KAAK;IACxDd,OAAO,EAAEc,IAAI,CAACd,OAAO;IACrBe,KAAK,EAAED,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC;EAChC,CAAC,CAAC,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACd,MAAMqB,MAAM,GAAG1B,KAAK,CAACqB,OAAO,CAAC,MAAMD,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACd,OAAO,CAAC,EAAE,CAACW,SAAS,CAAC,CAAC;EACpF;EACA,MAAMO,SAAS,GAAG3B,KAAK,CAAC4B,MAAM,CAACR,SAAS,CAAC;EACzC;EACA,MAAMS,WAAW,GAAGA,CAAC;IACnBC,SAAS;IACTC,aAAa;IACbC;EACF,CAAC,KAAK;IACJ,IAAIF,SAAS,CAACG,MAAM,GAAGb,SAAS,CAACa,MAAM,EAAE;MACvC;MACA,MAAMC,aAAa,GAAGhC,uBAAuB,CAACkB,SAAS,EAAEY,aAAa,CAAC;MACvE,MAAMG,UAAU,GAAGpC,kBAAkB,CAACqB,SAAS,CAAC;MAChDe,UAAU,CAACC,MAAM,CAACL,aAAa,EAAE,CAAC,EAAE;QAClCtB,OAAO,EAAEuB,aAAa;QACtBR,KAAK,EAAEU;MACT,CAAC,CAAC;MACFP,SAAS,CAACU,OAAO,GAAGF,UAAU;IAChC,CAAC,MAAM;MACLR,SAAS,CAACU,OAAO,GAAGjB,SAAS;IAC/B;IACAF,kBAAkB,CAAC,IAAI,CAAC;IACxBJ,QAAQ,CAAC,IAAIb,gBAAgB,CAACG,UAAU,CAACuB,SAAS,CAACU,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;EACrE,CAAC;EACD;EACA,MAAMC,YAAY,GAAGA,CAAC;IACpBC,WAAW;IACXR,aAAa;IACbC;EACF,CAAC,KAAK;IACJ,IAAIG,UAAU,GAAGpC,kBAAkB,CAAC4B,SAAS,CAACU,OAAO,CAAC;IACtD,IAAIE,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBJ,UAAU,CAACC,MAAM,CAACG,WAAW,EAAE,CAAC,CAAC;IACnC,CAAC,MAAM;MACLJ,UAAU,CAACJ,aAAa,CAAC,GAAGS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,UAAU,CAACJ,aAAa,CAAC,CAAC,EAAE;QACtFtB,OAAO,EAAEuB;MACX,CAAC,CAAC;MACFG,UAAU,GAAG/B,UAAU,CAAC+B,UAAU,CAAC;IACrC;IACArB,QAAQ,CAAC,IAAIb,gBAAgB,CAACkC,UAAU,CAAC,EAAE,IAAI,CAAC;EAClD,CAAC;EACD;EACA,MAAMO,WAAW,GAAGC,KAAK,IAAI;IAC3B,MAAMR,UAAU,GAAGpC,kBAAkB,CAACqB,SAAS,CAAC;IAChDe,UAAU,CAACC,MAAM,CAACO,KAAK,EAAE,CAAC,CAAC;IAC3B,MAAMC,SAAS,GAAG,IAAI3C,gBAAgB,CAACkC,UAAU,CAAC;IAClDrB,QAAQ,CAAC8B,SAAS,CAAC;IACnB7B,gBAAgB,CAAC6B,SAAS,CAAC;EAC7B,CAAC;EACD;EACA,MAAMC,wBAAwB,GAAGC,UAAU,IAAI;IAC7C/B,gBAAgB,CAAC,IAAId,gBAAgB,CAACmB,SAAS,CAAC,CAAC;IACjD;IACA,IAAIH,WAAW,IAAI6B,UAAU,CAACb,MAAM,EAAE;MACpCjB,QAAQ,CAAC8B,UAAU,CAACb,MAAM,GAAG,CAAC,CAAC;IACjC;IACAf,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EACD;EACA,IAAI,CAACC,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,OAAO,aAAanB,KAAK,CAAC+C,aAAa,CAAC5C,mBAAmB,EAAE;IAC3D6C,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,GAAG;IACRrC,SAAS,EAAEA,SAAS;IACpBsC,SAAS,EAAE,GAAGtC,SAAS,kBAAkB;IACzCP,MAAM,EAAEe,SAAS;IACjBI,KAAK,EAAE,IAAI;IACX2B,KAAK,EAAEzB,MAAM;IACb0B,KAAK,EAAE,IAAI;IACXrC,gBAAgB,EAAE8B,wBAAwB;IAC1CQ,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,UAAU;IAChB;IACArC,WAAW,EAAEA,WAAW;IACxBD,QAAQ,EAAEA,QAAQ;IAClB;IACAa,WAAW,EAAEA,WAAW;IACxBS,YAAY,EAAEA,YAAY;IAC1BI,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ,CAAC;AACD,eAAe,aAAa1C,KAAK,CAACuD,IAAI,CAAC7C,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}