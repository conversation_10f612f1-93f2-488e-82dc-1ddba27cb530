{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "getColorAlpha", "ColorSteppers", "ColorAlphaInput", "prefixCls", "value", "onChange", "colorAlphaInputPrefixCls", "internalValue", "setInternalValue", "alphaValue", "handleAlphaChange", "step", "hsba", "toHsb", "a", "genColor", "createElement", "formatter", "className"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/antd/es/color-picker/components/ColorAlphaInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,aAAa,QAAQ,SAAS;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,eAAe,GAAGA,CAAC;EACvBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,wBAAwB,GAAG,GAAGH,SAAS,cAAc;EAC3D,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,MAAMC,aAAa,CAACK,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMK,UAAU,GAAGL,KAAK,IAAIG,aAAa;EACzC,MAAMG,iBAAiB,GAAGC,IAAI,IAAI;IAChC,MAAMC,IAAI,GAAGH,UAAU,CAACI,KAAK,CAAC,CAAC;IAC/BD,IAAI,CAACE,CAAC,GAAG,CAACH,IAAI,IAAI,CAAC,IAAI,GAAG;IAC1B,MAAMI,QAAQ,GAAGhB,aAAa,CAACa,IAAI,CAAC;IACpCJ,gBAAgB,CAACO,QAAQ,CAAC;IAC1BV,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IACrDG,KAAK,EAAEJ,aAAa,CAACS,UAAU,CAAC;IAChCN,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEN,IAAI,IAAI,GAAGA,IAAI,GAAG;IAC7BO,SAAS,EAAEZ,wBAAwB;IACnCD,QAAQ,EAAEK;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}