{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport useTimeInfo from \"../../hooks/useTimeInfo\";\nimport PickerContext from \"../context\";\nexport default function Footer(props) {\n  var mode = props.mode,\n    internalMode = props.internalMode,\n    renderExtraFooter = props.renderExtraFooter,\n    showNow = props.showNow,\n    showTime = props.showTime,\n    onSubmit = props.onSubmit,\n    onNow = props.onNow,\n    invalid = props.invalid,\n    needConfirm = props.needConfirm,\n    generateConfig = props.generateConfig,\n    disabledDate = props.disabledDate;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls,\n    locale = _React$useContext.locale,\n    _React$useContext$but = _React$useContext.button,\n    Button = _React$useContext$but === void 0 ? 'button' : _React$useContext$but;\n\n  // >>> Now\n  var now = generateConfig.getNow();\n  var _useTimeInfo = useTimeInfo(generateConfig, showTime, now),\n    _useTimeInfo2 = _slicedToArray(_useTimeInfo, 1),\n    getValidTime = _useTimeInfo2[0];\n\n  // ======================== Extra =========================\n  var extraNode = renderExtraFooter === null || renderExtraFooter === void 0 ? void 0 : renderExtraFooter(mode);\n\n  // ======================== Ranges ========================\n  var nowDisabled = disabledDate(now, {\n    type: mode\n  });\n  var onInternalNow = function onInternalNow() {\n    if (!nowDisabled) {\n      var validateNow = getValidTime(now);\n      onNow(validateNow);\n    }\n  };\n  var nowPrefixCls = \"\".concat(prefixCls, \"-now\");\n  var nowBtnPrefixCls = \"\".concat(nowPrefixCls, \"-btn\");\n  var presetNode = showNow && /*#__PURE__*/React.createElement(\"li\", {\n    className: nowPrefixCls\n  }, /*#__PURE__*/React.createElement(\"a\", {\n    className: classNames(nowBtnPrefixCls, nowDisabled && \"\".concat(nowBtnPrefixCls, \"-disabled\")),\n    \"aria-disabled\": nowDisabled,\n    onClick: onInternalNow\n  }, internalMode === 'date' ? locale.today : locale.now));\n\n  // >>> OK\n  var okNode = needConfirm && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-ok\")\n  }, /*#__PURE__*/React.createElement(Button, {\n    disabled: invalid,\n    onClick: onSubmit\n  }, locale.ok));\n  var rangeNode = (presetNode || okNode) && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n\n  // ======================== Render ========================\n  if (!extraNode && !rangeNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, extraNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer-extra\")\n  }, extraNode), rangeNode);\n}", "map": {"version": 3, "names": ["_slicedToArray", "classNames", "React", "useTimeInfo", "<PERSON>er<PERSON>ontext", "Footer", "props", "mode", "internalMode", "renderExtraFooter", "showNow", "showTime", "onSubmit", "onNow", "invalid", "needConfirm", "generateConfig", "disabledDate", "_React$useContext", "useContext", "prefixCls", "locale", "_React$useContext$but", "button", "<PERSON><PERSON>", "now", "getNow", "_useTimeInfo", "_useTimeInfo2", "getValidTime", "extraNode", "nowDisabled", "type", "onInternalNow", "validateNow", "nowPrefixCls", "concat", "nowBtnPrefixCls", "presetNode", "createElement", "className", "onClick", "today", "okNode", "disabled", "ok", "rangeNode"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/rc-picker/es/PickerInput/Popup/Footer.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport useTimeInfo from \"../../hooks/useTimeInfo\";\nimport PickerContext from \"../context\";\nexport default function Footer(props) {\n  var mode = props.mode,\n    internalMode = props.internalMode,\n    renderExtraFooter = props.renderExtraFooter,\n    showNow = props.showNow,\n    showTime = props.showTime,\n    onSubmit = props.onSubmit,\n    onNow = props.onNow,\n    invalid = props.invalid,\n    needConfirm = props.needConfirm,\n    generateConfig = props.generateConfig,\n    disabledDate = props.disabledDate;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls,\n    locale = _React$useContext.locale,\n    _React$useContext$but = _React$useContext.button,\n    Button = _React$useContext$but === void 0 ? 'button' : _React$useContext$but;\n\n  // >>> Now\n  var now = generateConfig.getNow();\n  var _useTimeInfo = useTimeInfo(generateConfig, showTime, now),\n    _useTimeInfo2 = _slicedToArray(_useTimeInfo, 1),\n    getValidTime = _useTimeInfo2[0];\n\n  // ======================== Extra =========================\n  var extraNode = renderExtraFooter === null || renderExtraFooter === void 0 ? void 0 : renderExtraFooter(mode);\n\n  // ======================== Ranges ========================\n  var nowDisabled = disabledDate(now, {\n    type: mode\n  });\n  var onInternalNow = function onInternalNow() {\n    if (!nowDisabled) {\n      var validateNow = getValidTime(now);\n      onNow(validateNow);\n    }\n  };\n  var nowPrefixCls = \"\".concat(prefixCls, \"-now\");\n  var nowBtnPrefixCls = \"\".concat(nowPrefixCls, \"-btn\");\n  var presetNode = showNow && /*#__PURE__*/React.createElement(\"li\", {\n    className: nowPrefixCls\n  }, /*#__PURE__*/React.createElement(\"a\", {\n    className: classNames(nowBtnPrefixCls, nowDisabled && \"\".concat(nowBtnPrefixCls, \"-disabled\")),\n    \"aria-disabled\": nowDisabled,\n    onClick: onInternalNow\n  }, internalMode === 'date' ? locale.today : locale.now));\n\n  // >>> OK\n  var okNode = needConfirm && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-ok\")\n  }, /*#__PURE__*/React.createElement(Button, {\n    disabled: invalid,\n    onClick: onSubmit\n  }, locale.ok));\n  var rangeNode = (presetNode || okNode) && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n\n  // ======================== Render ========================\n  if (!extraNode && !rangeNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, extraNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer-extra\")\n  }, extraNode), rangeNode);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,aAAa,MAAM,YAAY;AACtC,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;IAC3CC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,YAAY,GAAGX,KAAK,CAACW,YAAY;EACnC,IAAIC,iBAAiB,GAAGhB,KAAK,CAACiB,UAAU,CAACf,aAAa,CAAC;IACrDgB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,MAAM,GAAGH,iBAAiB,CAACG,MAAM;IACjCC,qBAAqB,GAAGJ,iBAAiB,CAACK,MAAM;IAChDC,MAAM,GAAGF,qBAAqB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,qBAAqB;;EAE9E;EACA,IAAIG,GAAG,GAAGT,cAAc,CAACU,MAAM,CAAC,CAAC;EACjC,IAAIC,YAAY,GAAGxB,WAAW,CAACa,cAAc,EAAEL,QAAQ,EAAEc,GAAG,CAAC;IAC3DG,aAAa,GAAG5B,cAAc,CAAC2B,YAAY,EAAE,CAAC,CAAC;IAC/CE,YAAY,GAAGD,aAAa,CAAC,CAAC,CAAC;;EAEjC;EACA,IAAIE,SAAS,GAAGrB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACF,IAAI,CAAC;;EAE7G;EACA,IAAIwB,WAAW,GAAGd,YAAY,CAACQ,GAAG,EAAE;IAClCO,IAAI,EAAEzB;EACR,CAAC,CAAC;EACF,IAAI0B,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI,CAACF,WAAW,EAAE;MAChB,IAAIG,WAAW,GAAGL,YAAY,CAACJ,GAAG,CAAC;MACnCZ,KAAK,CAACqB,WAAW,CAAC;IACpB;EACF,CAAC;EACD,IAAIC,YAAY,GAAG,EAAE,CAACC,MAAM,CAAChB,SAAS,EAAE,MAAM,CAAC;EAC/C,IAAIiB,eAAe,GAAG,EAAE,CAACD,MAAM,CAACD,YAAY,EAAE,MAAM,CAAC;EACrD,IAAIG,UAAU,GAAG5B,OAAO,IAAI,aAAaR,KAAK,CAACqC,aAAa,CAAC,IAAI,EAAE;IACjEC,SAAS,EAAEL;EACb,CAAC,EAAE,aAAajC,KAAK,CAACqC,aAAa,CAAC,GAAG,EAAE;IACvCC,SAAS,EAAEvC,UAAU,CAACoC,eAAe,EAAEN,WAAW,IAAI,EAAE,CAACK,MAAM,CAACC,eAAe,EAAE,WAAW,CAAC,CAAC;IAC9F,eAAe,EAAEN,WAAW;IAC5BU,OAAO,EAAER;EACX,CAAC,EAAEzB,YAAY,KAAK,MAAM,GAAGa,MAAM,CAACqB,KAAK,GAAGrB,MAAM,CAACI,GAAG,CAAC,CAAC;;EAExD;EACA,IAAIkB,MAAM,GAAG5B,WAAW,IAAI,aAAab,KAAK,CAACqC,aAAa,CAAC,IAAI,EAAE;IACjEC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAAChB,SAAS,EAAE,KAAK;EACvC,CAAC,EAAE,aAAalB,KAAK,CAACqC,aAAa,CAACf,MAAM,EAAE;IAC1CoB,QAAQ,EAAE9B,OAAO;IACjB2B,OAAO,EAAE7B;EACX,CAAC,EAAES,MAAM,CAACwB,EAAE,CAAC,CAAC;EACd,IAAIC,SAAS,GAAG,CAACR,UAAU,IAAIK,MAAM,KAAK,aAAazC,KAAK,CAACqC,aAAa,CAAC,IAAI,EAAE;IAC/EC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAAChB,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEkB,UAAU,EAAEK,MAAM,CAAC;;EAEtB;EACA,IAAI,CAACb,SAAS,IAAI,CAACgB,SAAS,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,OAAO,aAAa5C,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAAChB,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEU,SAAS,IAAI,aAAa5B,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;IACtDC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAAChB,SAAS,EAAE,eAAe;EACjD,CAAC,EAAEU,SAAS,CAAC,EAAEgB,SAAS,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}