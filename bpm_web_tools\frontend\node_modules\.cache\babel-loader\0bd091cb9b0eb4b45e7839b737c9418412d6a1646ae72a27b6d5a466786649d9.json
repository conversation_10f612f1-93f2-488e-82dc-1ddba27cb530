{"ast": null, "code": "import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport composeSignals from \"../helpers/composeSignals.js\";\nimport { trackStream } from \"../helpers/trackStream.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport { progressEventReducer, progressEventDecorator, asyncDecorator } from \"../helpers/progressEventReducer.js\";\nimport resolveConfig from \"../helpers/resolveConfig.js\";\nimport settle from \"../core/settle.js\";\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\nconst {\n  isFunction\n} = utils;\nconst globalFetchAPI = (({\n  Request,\n  Response\n}) => ({\n  Request,\n  Response\n}))(utils.global);\nconst {\n  ReadableStream,\n  TextEncoder\n} = utils.global;\nconst test = (fn, ...args) => {\n  try {\n    return !!fn(...args);\n  } catch (e) {\n    return false;\n  }\n};\nconst factory = env => {\n  env = utils.merge.call({\n    skipUndefined: true\n  }, globalFetchAPI, env);\n  const {\n    fetch: envFetch,\n    Request,\n    Response\n  } = env;\n  const isFetchSupported = envFetch ? isFunction(envFetch) : typeof fetch === 'function';\n  const isRequestSupported = isFunction(Request);\n  const isResponseSupported = isFunction(Response);\n  if (!isFetchSupported) {\n    return false;\n  }\n  const isReadableStreamSupported = isFetchSupported && isFunction(ReadableStream);\n  const encodeText = isFetchSupported && (typeof TextEncoder === 'function' ? (encoder => str => encoder.encode(str))(new TextEncoder()) : async str => new Uint8Array(await new Request(str).arrayBuffer()));\n  const supportsRequestStream = isRequestSupported && isReadableStreamSupported && test(() => {\n    let duplexAccessed = false;\n    const hasContentType = new Request(platform.origin, {\n      body: new ReadableStream(),\n      method: 'POST',\n      get duplex() {\n        duplexAccessed = true;\n        return 'half';\n      }\n    }).headers.has('Content-Type');\n    return duplexAccessed && !hasContentType;\n  });\n  const supportsResponseStream = isResponseSupported && isReadableStreamSupported && test(() => utils.isReadableStream(new Response('').body));\n  const resolvers = {\n    stream: supportsResponseStream && (res => res.body)\n  };\n  isFetchSupported && (() => {\n    ['text', 'arrayBuffer', 'blob', 'formData', 'stream'].forEach(type => {\n      !resolvers[type] && (resolvers[type] = (res, config) => {\n        let method = res && res[type];\n        if (method) {\n          return method.call(res);\n        }\n        throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n      });\n    });\n  })();\n  const getBodyLength = async body => {\n    if (body == null) {\n      return 0;\n    }\n    if (utils.isBlob(body)) {\n      return body.size;\n    }\n    if (utils.isSpecCompliantForm(body)) {\n      const _request = new Request(platform.origin, {\n        method: 'POST',\n        body\n      });\n      return (await _request.arrayBuffer()).byteLength;\n    }\n    if (utils.isArrayBufferView(body) || utils.isArrayBuffer(body)) {\n      return body.byteLength;\n    }\n    if (utils.isURLSearchParams(body)) {\n      body = body + '';\n    }\n    if (utils.isString(body)) {\n      return (await encodeText(body)).byteLength;\n    }\n  };\n  const resolveBodyLength = async (headers, body) => {\n    const length = utils.toFiniteNumber(headers.getContentLength());\n    return length == null ? getBodyLength(body) : length;\n  };\n  return async config => {\n    let {\n      url,\n      method,\n      data,\n      signal,\n      cancelToken,\n      timeout,\n      onDownloadProgress,\n      onUploadProgress,\n      responseType,\n      headers,\n      withCredentials = 'same-origin',\n      fetchOptions\n    } = resolveConfig(config);\n    let _fetch = envFetch || fetch;\n    responseType = responseType ? (responseType + '').toLowerCase() : 'text';\n    let composedSignal = composeSignals([signal, cancelToken && cancelToken.toAbortSignal()], timeout);\n    let request = null;\n    const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {\n      composedSignal.unsubscribe();\n    });\n    let requestContentLength;\n    try {\n      if (onUploadProgress && supportsRequestStream && method !== 'get' && method !== 'head' && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {\n        let _request = new Request(url, {\n          method: 'POST',\n          body: data,\n          duplex: \"half\"\n        });\n        let contentTypeHeader;\n        if (utils.isFormData(data) && (contentTypeHeader = _request.headers.get('content-type'))) {\n          headers.setContentType(contentTypeHeader);\n        }\n        if (_request.body) {\n          const [onProgress, flush] = progressEventDecorator(requestContentLength, progressEventReducer(asyncDecorator(onUploadProgress)));\n          data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n        }\n      }\n      if (!utils.isString(withCredentials)) {\n        withCredentials = withCredentials ? 'include' : 'omit';\n      }\n\n      // Cloudflare Workers throws when credentials are defined\n      // see https://github.com/cloudflare/workerd/issues/902\n      const isCredentialsSupported = isRequestSupported && \"credentials\" in Request.prototype;\n      const resolvedOptions = {\n        ...fetchOptions,\n        signal: composedSignal,\n        method: method.toUpperCase(),\n        headers: headers.normalize().toJSON(),\n        body: data,\n        duplex: \"half\",\n        credentials: isCredentialsSupported ? withCredentials : undefined\n      };\n      request = isRequestSupported && new Request(url, resolvedOptions);\n      let response = await (isRequestSupported ? _fetch(request, fetchOptions) : _fetch(url, resolvedOptions));\n      const isStreamResponse = supportsResponseStream && (responseType === 'stream' || responseType === 'response');\n      if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {\n        const options = {};\n        ['status', 'statusText', 'headers'].forEach(prop => {\n          options[prop] = response[prop];\n        });\n        const responseContentLength = utils.toFiniteNumber(response.headers.get('content-length'));\n        const [onProgress, flush] = onDownloadProgress && progressEventDecorator(responseContentLength, progressEventReducer(asyncDecorator(onDownloadProgress), true)) || [];\n        response = new Response(trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {\n          flush && flush();\n          unsubscribe && unsubscribe();\n        }), options);\n      }\n      responseType = responseType || 'text';\n      let responseData = await resolvers[utils.findKey(resolvers, responseType) || 'text'](response, config);\n      !isStreamResponse && unsubscribe && unsubscribe();\n      return await new Promise((resolve, reject) => {\n        settle(resolve, reject, {\n          data: responseData,\n          headers: AxiosHeaders.from(response.headers),\n          status: response.status,\n          statusText: response.statusText,\n          config,\n          request\n        });\n      });\n    } catch (err) {\n      unsubscribe && unsubscribe();\n      if (err && err.name === 'TypeError' && /Load failed|fetch/i.test(err.message)) {\n        throw Object.assign(new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request), {\n          cause: err.cause || err\n        });\n      }\n      throw AxiosError.from(err, err && err.code, config, request);\n    }\n  };\n};\nconst seedCache = new Map();\nexport const getFetch = config => {\n  let env = config ? config.env : {};\n  const {\n    fetch,\n    Request,\n    Response\n  } = env;\n  const seeds = [Request, Response, fetch];\n  let len = seeds.length,\n    i = len,\n    seed,\n    target,\n    map = seedCache;\n  while (i--) {\n    seed = seeds[i];\n    target = map.get(seed);\n    target === undefined && map.set(seed, target = i ? new Map() : factory(env));\n    map = target;\n  }\n  return target;\n};\nconst adapter = getFetch();\nexport default adapter;", "map": {"version": 3, "names": ["platform", "utils", "AxiosError", "composeSignals", "trackStream", "AxiosHeaders", "progressEventReducer", "progressEventDecorator", "asyncDecorator", "resolveConfig", "settle", "DEFAULT_CHUNK_SIZE", "isFunction", "globalFetchAPI", "Request", "Response", "global", "ReadableStream", "TextEncoder", "test", "fn", "args", "e", "factory", "env", "merge", "call", "skipUndefined", "fetch", "envFetch", "isFetchSupported", "isRequestSupported", "isResponseSupported", "isReadableStreamSupported", "encodeText", "encoder", "str", "encode", "Uint8Array", "arrayBuffer", "supportsRequestStream", "duplexAccessed", "hasContentType", "origin", "body", "method", "duplex", "headers", "has", "supportsResponseStream", "isReadableStream", "resolvers", "stream", "res", "for<PERSON>ach", "type", "config", "ERR_NOT_SUPPORT", "getBody<PERSON><PERSON>th", "isBlob", "size", "isSpecCompliantForm", "_request", "byteLength", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isURLSearchParams", "isString", "resolveBody<PERSON><PERSON>th", "length", "toFiniteNumber", "getContentLength", "url", "data", "signal", "cancelToken", "timeout", "onDownloadProgress", "onUploadProgress", "responseType", "withCredentials", "fetchOptions", "_fetch", "toLowerCase", "composedSignal", "toAbortSignal", "request", "unsubscribe", "requestContentLength", "contentTypeHeader", "isFormData", "get", "setContentType", "onProgress", "flush", "isCredentialsSupported", "prototype", "resolvedOptions", "toUpperCase", "normalize", "toJSON", "credentials", "undefined", "response", "isStreamResponse", "options", "prop", "responseContentLength", "responseData", "<PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "from", "status", "statusText", "err", "name", "message", "Object", "assign", "ERR_NETWORK", "cause", "code", "seedCache", "Map", "getFetch", "seeds", "len", "i", "seed", "target", "map", "set", "adapter"], "sources": ["D:/augment_prj/bpm_easy_tools/bpm_web_tools/frontend/node_modules/axios/lib/adapters/fetch.js"], "sourcesContent": ["import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport composeSignals from \"../helpers/composeSignals.js\";\nimport {trackStream} from \"../helpers/trackStream.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport {progressEventReducer, progressEventDecorator, asyncDecorator} from \"../helpers/progressEventReducer.js\";\nimport resolveConfig from \"../helpers/resolveConfig.js\";\nimport settle from \"../core/settle.js\";\n\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\n\nconst {isFunction} = utils;\n\nconst globalFetchAPI = (({Request, Response}) => ({\n  Request, Response\n}))(utils.global);\n\nconst {\n  ReadableStream, TextEncoder\n} = utils.global;\n\n\nconst test = (fn, ...args) => {\n  try {\n    return !!fn(...args);\n  } catch (e) {\n    return false\n  }\n}\n\nconst factory = (env) => {\n  env = utils.merge.call({\n    skipUndefined: true\n  }, globalFetchAPI, env);\n\n  const {fetch: envFetch, Request, Response} = env;\n  const isFetchSupported = envFetch ? isFunction(envFetch) : typeof fetch === 'function';\n  const isRequestSupported = isFunction(Request);\n  const isResponseSupported = isFunction(Response);\n\n  if (!isFetchSupported) {\n    return false;\n  }\n\n  const isReadableStreamSupported = isFetchSupported && isFunction(ReadableStream);\n\n  const encodeText = isFetchSupported && (typeof TextEncoder === 'function' ?\n      ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) :\n      async (str) => new Uint8Array(await new Request(str).arrayBuffer())\n  );\n\n  const supportsRequestStream = isRequestSupported && isReadableStreamSupported && test(() => {\n    let duplexAccessed = false;\n\n    const hasContentType = new Request(platform.origin, {\n      body: new ReadableStream(),\n      method: 'POST',\n      get duplex() {\n        duplexAccessed = true;\n        return 'half';\n      },\n    }).headers.has('Content-Type');\n\n    return duplexAccessed && !hasContentType;\n  });\n\n  const supportsResponseStream = isResponseSupported && isReadableStreamSupported &&\n    test(() => utils.isReadableStream(new Response('').body));\n\n  const resolvers = {\n    stream: supportsResponseStream && ((res) => res.body)\n  };\n\n  isFetchSupported && ((() => {\n    ['text', 'arrayBuffer', 'blob', 'formData', 'stream'].forEach(type => {\n      !resolvers[type] && (resolvers[type] = (res, config) => {\n        let method = res && res[type];\n\n        if (method) {\n          return method.call(res);\n        }\n\n        throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n      })\n    });\n  })());\n\n  const getBodyLength = async (body) => {\n    if (body == null) {\n      return 0;\n    }\n\n    if (utils.isBlob(body)) {\n      return body.size;\n    }\n\n    if (utils.isSpecCompliantForm(body)) {\n      const _request = new Request(platform.origin, {\n        method: 'POST',\n        body,\n      });\n      return (await _request.arrayBuffer()).byteLength;\n    }\n\n    if (utils.isArrayBufferView(body) || utils.isArrayBuffer(body)) {\n      return body.byteLength;\n    }\n\n    if (utils.isURLSearchParams(body)) {\n      body = body + '';\n    }\n\n    if (utils.isString(body)) {\n      return (await encodeText(body)).byteLength;\n    }\n  }\n\n  const resolveBodyLength = async (headers, body) => {\n    const length = utils.toFiniteNumber(headers.getContentLength());\n\n    return length == null ? getBodyLength(body) : length;\n  }\n\n  return async (config) => {\n    let {\n      url,\n      method,\n      data,\n      signal,\n      cancelToken,\n      timeout,\n      onDownloadProgress,\n      onUploadProgress,\n      responseType,\n      headers,\n      withCredentials = 'same-origin',\n      fetchOptions\n    } = resolveConfig(config);\n\n    let _fetch = envFetch || fetch;\n\n    responseType = responseType ? (responseType + '').toLowerCase() : 'text';\n\n    let composedSignal = composeSignals([signal, cancelToken && cancelToken.toAbortSignal()], timeout);\n\n    let request = null;\n\n    const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {\n      composedSignal.unsubscribe();\n    });\n\n    let requestContentLength;\n\n    try {\n      if (\n        onUploadProgress && supportsRequestStream && method !== 'get' && method !== 'head' &&\n        (requestContentLength = await resolveBodyLength(headers, data)) !== 0\n      ) {\n        let _request = new Request(url, {\n          method: 'POST',\n          body: data,\n          duplex: \"half\"\n        });\n\n        let contentTypeHeader;\n\n        if (utils.isFormData(data) && (contentTypeHeader = _request.headers.get('content-type'))) {\n          headers.setContentType(contentTypeHeader)\n        }\n\n        if (_request.body) {\n          const [onProgress, flush] = progressEventDecorator(\n            requestContentLength,\n            progressEventReducer(asyncDecorator(onUploadProgress))\n          );\n\n          data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n        }\n      }\n\n      if (!utils.isString(withCredentials)) {\n        withCredentials = withCredentials ? 'include' : 'omit';\n      }\n\n      // Cloudflare Workers throws when credentials are defined\n      // see https://github.com/cloudflare/workerd/issues/902\n      const isCredentialsSupported = isRequestSupported && \"credentials\" in Request.prototype;\n\n      const resolvedOptions = {\n        ...fetchOptions,\n        signal: composedSignal,\n        method: method.toUpperCase(),\n        headers: headers.normalize().toJSON(),\n        body: data,\n        duplex: \"half\",\n        credentials: isCredentialsSupported ? withCredentials : undefined\n      };\n\n      request = isRequestSupported && new Request(url, resolvedOptions);\n\n      let response = await (isRequestSupported ? _fetch(request, fetchOptions) : _fetch(url, resolvedOptions));\n\n      const isStreamResponse = supportsResponseStream && (responseType === 'stream' || responseType === 'response');\n\n      if (supportsResponseStream && (onDownloadProgress || (isStreamResponse && unsubscribe))) {\n        const options = {};\n\n        ['status', 'statusText', 'headers'].forEach(prop => {\n          options[prop] = response[prop];\n        });\n\n        const responseContentLength = utils.toFiniteNumber(response.headers.get('content-length'));\n\n        const [onProgress, flush] = onDownloadProgress && progressEventDecorator(\n          responseContentLength,\n          progressEventReducer(asyncDecorator(onDownloadProgress), true)\n        ) || [];\n\n        response = new Response(\n          trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {\n            flush && flush();\n            unsubscribe && unsubscribe();\n          }),\n          options\n        );\n      }\n\n      responseType = responseType || 'text';\n\n      let responseData = await resolvers[utils.findKey(resolvers, responseType) || 'text'](response, config);\n\n      !isStreamResponse && unsubscribe && unsubscribe();\n\n      return await new Promise((resolve, reject) => {\n        settle(resolve, reject, {\n          data: responseData,\n          headers: AxiosHeaders.from(response.headers),\n          status: response.status,\n          statusText: response.statusText,\n          config,\n          request\n        })\n      })\n    } catch (err) {\n      unsubscribe && unsubscribe();\n\n      if (err && err.name === 'TypeError' && /Load failed|fetch/i.test(err.message)) {\n        throw Object.assign(\n          new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request),\n          {\n            cause: err.cause || err\n          }\n        )\n      }\n\n      throw AxiosError.from(err, err && err.code, config, request);\n    }\n  }\n}\n\nconst seedCache = new Map();\n\nexport const getFetch = (config) => {\n  let env = config ? config.env : {};\n  const {fetch, Request, Response} = env;\n  const seeds = [\n    Request, Response, fetch\n  ];\n\n  let len = seeds.length, i = len,\n    seed, target, map = seedCache;\n\n  while (i--) {\n    seed = seeds[i];\n    target = map.get(seed);\n\n    target === undefined && map.set(seed, target = (i ? new Map() : factory(env)))\n\n    map = target;\n  }\n\n  return target;\n};\n\nconst adapter = getFetch();\n\nexport default adapter;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAAQC,WAAW,QAAO,2BAA2B;AACrD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAAQC,oBAAoB,EAAEC,sBAAsB,EAAEC,cAAc,QAAO,oCAAoC;AAC/G,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,MAAMC,kBAAkB,GAAG,EAAE,GAAG,IAAI;AAEpC,MAAM;EAACC;AAAU,CAAC,GAAGX,KAAK;AAE1B,MAAMY,cAAc,GAAG,CAAC,CAAC;EAACC,OAAO;EAAEC;AAAQ,CAAC,MAAM;EAChDD,OAAO;EAAEC;AACX,CAAC,CAAC,EAAEd,KAAK,CAACe,MAAM,CAAC;AAEjB,MAAM;EACJC,cAAc;EAAEC;AAClB,CAAC,GAAGjB,KAAK,CAACe,MAAM;AAGhB,MAAMG,IAAI,GAAGA,CAACC,EAAE,EAAE,GAAGC,IAAI,KAAK;EAC5B,IAAI;IACF,OAAO,CAAC,CAACD,EAAE,CAAC,GAAGC,IAAI,CAAC;EACtB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF,CAAC;AAED,MAAMC,OAAO,GAAIC,GAAG,IAAK;EACvBA,GAAG,GAAGvB,KAAK,CAACwB,KAAK,CAACC,IAAI,CAAC;IACrBC,aAAa,EAAE;EACjB,CAAC,EAAEd,cAAc,EAAEW,GAAG,CAAC;EAEvB,MAAM;IAACI,KAAK,EAAEC,QAAQ;IAAEf,OAAO;IAAEC;EAAQ,CAAC,GAAGS,GAAG;EAChD,MAAMM,gBAAgB,GAAGD,QAAQ,GAAGjB,UAAU,CAACiB,QAAQ,CAAC,GAAG,OAAOD,KAAK,KAAK,UAAU;EACtF,MAAMG,kBAAkB,GAAGnB,UAAU,CAACE,OAAO,CAAC;EAC9C,MAAMkB,mBAAmB,GAAGpB,UAAU,CAACG,QAAQ,CAAC;EAEhD,IAAI,CAACe,gBAAgB,EAAE;IACrB,OAAO,KAAK;EACd;EAEA,MAAMG,yBAAyB,GAAGH,gBAAgB,IAAIlB,UAAU,CAACK,cAAc,CAAC;EAEhF,MAAMiB,UAAU,GAAGJ,gBAAgB,KAAK,OAAOZ,WAAW,KAAK,UAAU,GACrE,CAAEiB,OAAO,IAAMC,GAAG,IAAKD,OAAO,CAACE,MAAM,CAACD,GAAG,CAAC,EAAE,IAAIlB,WAAW,CAAC,CAAC,CAAC,GAC9D,MAAOkB,GAAG,IAAK,IAAIE,UAAU,CAAC,MAAM,IAAIxB,OAAO,CAACsB,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC,CACtE;EAED,MAAMC,qBAAqB,GAAGT,kBAAkB,IAAIE,yBAAyB,IAAId,IAAI,CAAC,MAAM;IAC1F,IAAIsB,cAAc,GAAG,KAAK;IAE1B,MAAMC,cAAc,GAAG,IAAI5B,OAAO,CAACd,QAAQ,CAAC2C,MAAM,EAAE;MAClDC,IAAI,EAAE,IAAI3B,cAAc,CAAC,CAAC;MAC1B4B,MAAM,EAAE,MAAM;MACd,IAAIC,MAAMA,CAAA,EAAG;QACXL,cAAc,GAAG,IAAI;QACrB,OAAO,MAAM;MACf;IACF,CAAC,CAAC,CAACM,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAE9B,OAAOP,cAAc,IAAI,CAACC,cAAc;EAC1C,CAAC,CAAC;EAEF,MAAMO,sBAAsB,GAAGjB,mBAAmB,IAAIC,yBAAyB,IAC7Ed,IAAI,CAAC,MAAMlB,KAAK,CAACiD,gBAAgB,CAAC,IAAInC,QAAQ,CAAC,EAAE,CAAC,CAAC6B,IAAI,CAAC,CAAC;EAE3D,MAAMO,SAAS,GAAG;IAChBC,MAAM,EAAEH,sBAAsB,KAAMI,GAAG,IAAKA,GAAG,CAACT,IAAI;EACtD,CAAC;EAEDd,gBAAgB,IAAK,CAAC,MAAM;IAC1B,CAAC,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACwB,OAAO,CAACC,IAAI,IAAI;MACpE,CAACJ,SAAS,CAACI,IAAI,CAAC,KAAKJ,SAAS,CAACI,IAAI,CAAC,GAAG,CAACF,GAAG,EAAEG,MAAM,KAAK;QACtD,IAAIX,MAAM,GAAGQ,GAAG,IAAIA,GAAG,CAACE,IAAI,CAAC;QAE7B,IAAIV,MAAM,EAAE;UACV,OAAOA,MAAM,CAACnB,IAAI,CAAC2B,GAAG,CAAC;QACzB;QAEA,MAAM,IAAInD,UAAU,CAAC,kBAAkBqD,IAAI,oBAAoB,EAAErD,UAAU,CAACuD,eAAe,EAAED,MAAM,CAAC;MACtG,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAE;EAEL,MAAME,aAAa,GAAG,MAAOd,IAAI,IAAK;IACpC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,CAAC;IACV;IAEA,IAAI3C,KAAK,CAAC0D,MAAM,CAACf,IAAI,CAAC,EAAE;MACtB,OAAOA,IAAI,CAACgB,IAAI;IAClB;IAEA,IAAI3D,KAAK,CAAC4D,mBAAmB,CAACjB,IAAI,CAAC,EAAE;MACnC,MAAMkB,QAAQ,GAAG,IAAIhD,OAAO,CAACd,QAAQ,CAAC2C,MAAM,EAAE;QAC5CE,MAAM,EAAE,MAAM;QACdD;MACF,CAAC,CAAC;MACF,OAAO,CAAC,MAAMkB,QAAQ,CAACvB,WAAW,CAAC,CAAC,EAAEwB,UAAU;IAClD;IAEA,IAAI9D,KAAK,CAAC+D,iBAAiB,CAACpB,IAAI,CAAC,IAAI3C,KAAK,CAACgE,aAAa,CAACrB,IAAI,CAAC,EAAE;MAC9D,OAAOA,IAAI,CAACmB,UAAU;IACxB;IAEA,IAAI9D,KAAK,CAACiE,iBAAiB,CAACtB,IAAI,CAAC,EAAE;MACjCA,IAAI,GAAGA,IAAI,GAAG,EAAE;IAClB;IAEA,IAAI3C,KAAK,CAACkE,QAAQ,CAACvB,IAAI,CAAC,EAAE;MACxB,OAAO,CAAC,MAAMV,UAAU,CAACU,IAAI,CAAC,EAAEmB,UAAU;IAC5C;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAOrB,OAAO,EAAEH,IAAI,KAAK;IACjD,MAAMyB,MAAM,GAAGpE,KAAK,CAACqE,cAAc,CAACvB,OAAO,CAACwB,gBAAgB,CAAC,CAAC,CAAC;IAE/D,OAAOF,MAAM,IAAI,IAAI,GAAGX,aAAa,CAACd,IAAI,CAAC,GAAGyB,MAAM;EACtD,CAAC;EAED,OAAO,MAAOb,MAAM,IAAK;IACvB,IAAI;MACFgB,GAAG;MACH3B,MAAM;MACN4B,IAAI;MACJC,MAAM;MACNC,WAAW;MACXC,OAAO;MACPC,kBAAkB;MAClBC,gBAAgB;MAChBC,YAAY;MACZhC,OAAO;MACPiC,eAAe,GAAG,aAAa;MAC/BC;IACF,CAAC,GAAGxE,aAAa,CAAC+C,MAAM,CAAC;IAEzB,IAAI0B,MAAM,GAAGrD,QAAQ,IAAID,KAAK;IAE9BmD,YAAY,GAAGA,YAAY,GAAG,CAACA,YAAY,GAAG,EAAE,EAAEI,WAAW,CAAC,CAAC,GAAG,MAAM;IAExE,IAAIC,cAAc,GAAGjF,cAAc,CAAC,CAACuE,MAAM,EAAEC,WAAW,IAAIA,WAAW,CAACU,aAAa,CAAC,CAAC,CAAC,EAAET,OAAO,CAAC;IAElG,IAAIU,OAAO,GAAG,IAAI;IAElB,MAAMC,WAAW,GAAGH,cAAc,IAAIA,cAAc,CAACG,WAAW,KAAK,MAAM;MACzEH,cAAc,CAACG,WAAW,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,IAAIC,oBAAoB;IAExB,IAAI;MACF,IACEV,gBAAgB,IAAItC,qBAAqB,IAAIK,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,IAClF,CAAC2C,oBAAoB,GAAG,MAAMpB,iBAAiB,CAACrB,OAAO,EAAE0B,IAAI,CAAC,MAAM,CAAC,EACrE;QACA,IAAIX,QAAQ,GAAG,IAAIhD,OAAO,CAAC0D,GAAG,EAAE;UAC9B3B,MAAM,EAAE,MAAM;UACdD,IAAI,EAAE6B,IAAI;UACV3B,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,IAAI2C,iBAAiB;QAErB,IAAIxF,KAAK,CAACyF,UAAU,CAACjB,IAAI,CAAC,KAAKgB,iBAAiB,GAAG3B,QAAQ,CAACf,OAAO,CAAC4C,GAAG,CAAC,cAAc,CAAC,CAAC,EAAE;UACxF5C,OAAO,CAAC6C,cAAc,CAACH,iBAAiB,CAAC;QAC3C;QAEA,IAAI3B,QAAQ,CAAClB,IAAI,EAAE;UACjB,MAAM,CAACiD,UAAU,EAAEC,KAAK,CAAC,GAAGvF,sBAAsB,CAChDiF,oBAAoB,EACpBlF,oBAAoB,CAACE,cAAc,CAACsE,gBAAgB,CAAC,CACvD,CAAC;UAEDL,IAAI,GAAGrE,WAAW,CAAC0D,QAAQ,CAAClB,IAAI,EAAEjC,kBAAkB,EAAEkF,UAAU,EAAEC,KAAK,CAAC;QAC1E;MACF;MAEA,IAAI,CAAC7F,KAAK,CAACkE,QAAQ,CAACa,eAAe,CAAC,EAAE;QACpCA,eAAe,GAAGA,eAAe,GAAG,SAAS,GAAG,MAAM;MACxD;;MAEA;MACA;MACA,MAAMe,sBAAsB,GAAGhE,kBAAkB,IAAI,aAAa,IAAIjB,OAAO,CAACkF,SAAS;MAEvF,MAAMC,eAAe,GAAG;QACtB,GAAGhB,YAAY;QACfP,MAAM,EAAEU,cAAc;QACtBvC,MAAM,EAAEA,MAAM,CAACqD,WAAW,CAAC,CAAC;QAC5BnD,OAAO,EAAEA,OAAO,CAACoD,SAAS,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;QACrCxD,IAAI,EAAE6B,IAAI;QACV3B,MAAM,EAAE,MAAM;QACduD,WAAW,EAAEN,sBAAsB,GAAGf,eAAe,GAAGsB;MAC1D,CAAC;MAEDhB,OAAO,GAAGvD,kBAAkB,IAAI,IAAIjB,OAAO,CAAC0D,GAAG,EAAEyB,eAAe,CAAC;MAEjE,IAAIM,QAAQ,GAAG,OAAOxE,kBAAkB,GAAGmD,MAAM,CAACI,OAAO,EAAEL,YAAY,CAAC,GAAGC,MAAM,CAACV,GAAG,EAAEyB,eAAe,CAAC,CAAC;MAExG,MAAMO,gBAAgB,GAAGvD,sBAAsB,KAAK8B,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,UAAU,CAAC;MAE7G,IAAI9B,sBAAsB,KAAK4B,kBAAkB,IAAK2B,gBAAgB,IAAIjB,WAAY,CAAC,EAAE;QACvF,MAAMkB,OAAO,GAAG,CAAC,CAAC;QAElB,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAACnD,OAAO,CAACoD,IAAI,IAAI;UAClDD,OAAO,CAACC,IAAI,CAAC,GAAGH,QAAQ,CAACG,IAAI,CAAC;QAChC,CAAC,CAAC;QAEF,MAAMC,qBAAqB,GAAG1G,KAAK,CAACqE,cAAc,CAACiC,QAAQ,CAACxD,OAAO,CAAC4C,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAE1F,MAAM,CAACE,UAAU,EAAEC,KAAK,CAAC,GAAGjB,kBAAkB,IAAItE,sBAAsB,CACtEoG,qBAAqB,EACrBrG,oBAAoB,CAACE,cAAc,CAACqE,kBAAkB,CAAC,EAAE,IAAI,CAC/D,CAAC,IAAI,EAAE;QAEP0B,QAAQ,GAAG,IAAIxF,QAAQ,CACrBX,WAAW,CAACmG,QAAQ,CAAC3D,IAAI,EAAEjC,kBAAkB,EAAEkF,UAAU,EAAE,MAAM;UAC/DC,KAAK,IAAIA,KAAK,CAAC,CAAC;UAChBP,WAAW,IAAIA,WAAW,CAAC,CAAC;QAC9B,CAAC,CAAC,EACFkB,OACF,CAAC;MACH;MAEA1B,YAAY,GAAGA,YAAY,IAAI,MAAM;MAErC,IAAI6B,YAAY,GAAG,MAAMzD,SAAS,CAAClD,KAAK,CAAC4G,OAAO,CAAC1D,SAAS,EAAE4B,YAAY,CAAC,IAAI,MAAM,CAAC,CAACwB,QAAQ,EAAE/C,MAAM,CAAC;MAEtG,CAACgD,gBAAgB,IAAIjB,WAAW,IAAIA,WAAW,CAAC,CAAC;MAEjD,OAAO,MAAM,IAAIuB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QAC5CtG,MAAM,CAACqG,OAAO,EAAEC,MAAM,EAAE;UACtBvC,IAAI,EAAEmC,YAAY;UAClB7D,OAAO,EAAE1C,YAAY,CAAC4G,IAAI,CAACV,QAAQ,CAACxD,OAAO,CAAC;UAC5CmE,MAAM,EAAEX,QAAQ,CAACW,MAAM;UACvBC,UAAU,EAAEZ,QAAQ,CAACY,UAAU;UAC/B3D,MAAM;UACN8B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO8B,GAAG,EAAE;MACZ7B,WAAW,IAAIA,WAAW,CAAC,CAAC;MAE5B,IAAI6B,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,WAAW,IAAI,oBAAoB,CAAClG,IAAI,CAACiG,GAAG,CAACE,OAAO,CAAC,EAAE;QAC7E,MAAMC,MAAM,CAACC,MAAM,CACjB,IAAItH,UAAU,CAAC,eAAe,EAAEA,UAAU,CAACuH,WAAW,EAAEjE,MAAM,EAAE8B,OAAO,CAAC,EACxE;UACEoC,KAAK,EAAEN,GAAG,CAACM,KAAK,IAAIN;QACtB,CACF,CAAC;MACH;MAEA,MAAMlH,UAAU,CAAC+G,IAAI,CAACG,GAAG,EAAEA,GAAG,IAAIA,GAAG,CAACO,IAAI,EAAEnE,MAAM,EAAE8B,OAAO,CAAC;IAC9D;EACF,CAAC;AACH,CAAC;AAED,MAAMsC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE3B,OAAO,MAAMC,QAAQ,GAAItE,MAAM,IAAK;EAClC,IAAIhC,GAAG,GAAGgC,MAAM,GAAGA,MAAM,CAAChC,GAAG,GAAG,CAAC,CAAC;EAClC,MAAM;IAACI,KAAK;IAAEd,OAAO;IAAEC;EAAQ,CAAC,GAAGS,GAAG;EACtC,MAAMuG,KAAK,GAAG,CACZjH,OAAO,EAAEC,QAAQ,EAAEa,KAAK,CACzB;EAED,IAAIoG,GAAG,GAAGD,KAAK,CAAC1D,MAAM;IAAE4D,CAAC,GAAGD,GAAG;IAC7BE,IAAI;IAAEC,MAAM;IAAEC,GAAG,GAAGR,SAAS;EAE/B,OAAOK,CAAC,EAAE,EAAE;IACVC,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC;IACfE,MAAM,GAAGC,GAAG,CAACzC,GAAG,CAACuC,IAAI,CAAC;IAEtBC,MAAM,KAAK7B,SAAS,IAAI8B,GAAG,CAACC,GAAG,CAACH,IAAI,EAAEC,MAAM,GAAIF,CAAC,GAAG,IAAIJ,GAAG,CAAC,CAAC,GAAGtG,OAAO,CAACC,GAAG,CAAE,CAAC;IAE9E4G,GAAG,GAAGD,MAAM;EACd;EAEA,OAAOA,MAAM;AACf,CAAC;AAED,MAAMG,OAAO,GAAGR,QAAQ,CAAC,CAAC;AAE1B,eAAeQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}